events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Upstream definitions for production services
    upstream mvs_api_gateway {
        server mvs_api_gateway:3000;
    }

    upstream mvs_directus {
        server mvs_directus:8055;
    }

    # Upstream definitions for staging services
    upstream staging_api_gateway {
        server staging_api_gateway:3000;
    }

    upstream staging_directus {
        server staging_directus:8055;
    }

    # PRODUCTION DOMAIN - mvs.kanousai.com
    server {
        listen 80;
        server_name mvs.kanousai.com;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Production - Healthy
Environment: Production
Domain: mvs.kanousai.com
Server Path: /root/mvs/
API: mvs.kanousai.com/api
Admin: mvs.kanousai.com/admin
Vendor: mvs.kanousai.com/vendor
";
            add_header Content-Type text/plain;
        }

        # API Gateway - Production
        location /api/ {
            proxy_pass http://mvs_api_gateway/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Admin Panel - Production (Directus)
        location /admin/ {
            proxy_pass http://mvs_directus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Vendor Portal - Production
        location /vendor/ {
            proxy_pass http://mvs_api_gateway/vendor/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Home Page - Production (Vendor/Client Login Options)
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Production Portal</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 10px; }
        .subtitle { text-align: center; color: #666; margin-bottom: 30px; }
        .status { background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #4caf50; }
        .login-options { display: flex; gap: 20px; margin: 30px 0; }
        .login-card { flex: 1; background: #f8f9fa; padding: 25px; border-radius: 10px; text-align: center; border: 2px solid #e9ecef; transition: all 0.3s; }
        .login-card:hover { border-color: #007bff; transform: translateY(-2px); }
        .login-card h3 { color: #333; margin-bottom: 15px; }
        .login-card p { color: #666; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
        .btn:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .links { text-align: center; margin: 30px 0; }
        .links a { color: #007bff; text-decoration: none; margin: 0 15px; }
        .environment { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; text-align: center; font-weight: bold; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 MVS-VR Platform</h1>
        <p class='subtitle'>Virtual Reality Content Management System</p>
        
        <div class='environment'>🟢 PRODUCTION ENVIRONMENT</div>
        
        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🌐 Domain:</strong> mvs.kanousai.com<br>
            <strong>📡 Server:</strong> 157.245.103.57 (/root/mvs/)<br>
            <strong>🔗 Database:</strong> Supabase Connected
        </div>
        
        <div class='login-options'>
            <div class='login-card'>
                <h3>🏢 Vendor Portal</h3>
                <p>Access vendor dashboard, manage VR content, and configure client experiences.</p>
                <a href='/vendor/' class='btn'>Vendor Login</a>
            </div>
            <div class='login-card'>
                <h3>👥 Client Access</h3>
                <p>View and interact with VR experiences created by your vendor.</p>
                <a href='/api/client-portal' class='btn btn-secondary'>Client Portal</a>
            </div>
        </div>
        
        <div class='links'>
            <a href='/api/health'>API Status</a> |
            <a href='/admin/'>System Admin</a> |
            <a href='http://stagingmvs.kanousai.com'>Staging Environment</a>
        </div>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # STAGING DOMAIN - stagingmvs.kanousai.com
    server {
        listen 80;
        server_name stagingmvs.kanousai.com;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Staging - Healthy
Environment: Staging
Domain: stagingmvs.kanousai.com
Server Path: /root/stagingmvs/
API: stagingmvs.kanousai.com/api
Admin: stagingmvs.kanousai.com/admin
Vendor: stagingmvs.kanousai.com/vendor
";
            add_header Content-Type text/plain;
        }

        # API Gateway - Staging
        location /api/ {
            proxy_pass http://staging_api_gateway/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Admin Panel - Staging
        location /admin/ {
            proxy_pass http://staging_directus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Vendor Portal - Staging
        location /vendor/ {
            proxy_pass http://staging_api_gateway/vendor/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Home Page - Staging
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Staging Environment</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 10px; }
        .subtitle { text-align: center; color: #666; margin-bottom: 30px; }
        .warning { background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #ffc107; }
        .status { background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #4caf50; }
        .login-options { display: flex; gap: 20px; margin: 30px 0; }
        .login-card { flex: 1; background: #f8f9fa; padding: 25px; border-radius: 10px; text-align: center; border: 2px solid #e9ecef; }
        .btn { display: inline-block; padding: 12px 24px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 5px; }
        .links { text-align: center; margin: 30px 0; }
        .links a { color: #ff6b6b; text-decoration: none; margin: 0 15px; }
        .environment { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; text-align: center; font-weight: bold; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 MVS-VR Staging</h1>
        <p class='subtitle'>Testing Environment</p>
        
        <div class='environment'>⚠️ STAGING ENVIRONMENT - FOR TESTING ONLY</div>
        
        <div class='warning'>
            <strong>⚠️ Warning:</strong> This is a staging environment for testing purposes only. Data may be reset without notice.
        </div>
        
        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🌐 Domain:</strong> stagingmvs.kanousai.com<br>
            <strong>📡 Server:</strong> 157.245.103.57 (/root/stagingmvs/)<br>
            <strong>🔗 Database:</strong> Staging Database Connected
        </div>
        
        <div class='login-options'>
            <div class='login-card'>
                <h3>🏢 Vendor Testing</h3>
                <p>Test vendor functionality and new features before production deployment.</p>
                <a href='/vendor/' class='btn'>Test Vendor Portal</a>
            </div>
            <div class='login-card'>
                <h3>👥 Client Testing</h3>
                <p>Test client experiences and validate functionality.</p>
                <a href='/api/client-portal' class='btn'>Test Client Portal</a>
            </div>
        </div>
        
        <div class='links'>
            <a href='/api/health'>API Status</a> |
            <a href='/admin/'>Admin Testing</a> |
            <a href='http://mvs.kanousai.com'>Production Environment</a>
        </div>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # DEFAULT/FALLBACK - Handle IP access and unknown domains
    server {
        listen 80 default_server;
        server_name _;

        # Redirect IP access to main domain
        location / {
            return 301 http://mvs.kanousai.com$request_uri;
        }

        # Health check for IP access
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure - Working
Production: mvs.kanousai.com (/root/mvs/)
Staging: stagingmvs.kanousai.com (/root/stagingmvs/)
Root Domain: kanousai.com (separate setup)
";
            add_header Content-Type text/plain;
        }
    }
}
