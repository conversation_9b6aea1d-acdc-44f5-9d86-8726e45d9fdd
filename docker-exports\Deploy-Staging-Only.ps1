# MVS-VR Staging-Only Deployment Script
# This script deploys only the staging environment with attached storage

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\.ssh\mvs-vr",
    [string]$LocalExportPath = "C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\docker-exports",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Test-SSHConnection {
    Write-ColorOutput "🔍 Testing SSH connection to $ServerIP..." $Cyan
    
    ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ SSH connection successful" $Green
        return $true
    } else {
        Write-ColorOutput "❌ SSH connection failed" $Red
        return $false
    }
}

function Setup-AttachedStorage {
    Write-ColorOutput "💾 Setting up attached storage..." $Cyan
    
    $storageCommands = @(
        "mkdir -p /mnt/volume_blr1_01",
        "mount -o discard,defaults /dev/disk/by-id/scsi-0DO_Volume_volume-blr1-01 /mnt/volume_blr1_01 2>/dev/null || echo 'Already mounted'",
        "grep -q 'volume-blr1-01' /etc/fstab || echo '/dev/disk/by-id/scsi-0DO_Volume_volume-blr1-01 /mnt/volume_blr1_01 ext4 defaults,nofail,discard 0 0' | tee -a /etc/fstab",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/docker-images",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/backups",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/staging-data",
        "chown -R root:root /mnt/volume_blr1_01/mvs-vr-project"
    )
    
    foreach ($cmd in $storageCommands) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would execute: $cmd" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP $cmd
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Storage setup: $cmd" $Green
            } else {
                Write-ColorOutput "⚠️ Storage command (may be normal): $cmd" $Yellow
            }
        }
    }
    
    # Verify storage is mounted
    if (-not $DryRun) {
        $mountCheck = ssh -i $SSHKeyPath $Username@$ServerIP "df -h | grep volume_blr1_01"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Attached storage mounted successfully" $Green
            Write-ColorOutput "Storage info: $mountCheck" $Cyan
        } else {
            Write-ColorOutput "❌ Attached storage mount verification failed" $Red
        }
    }
}

function Create-StagingDirectories {
    Write-ColorOutput "📁 Creating staging directory structure..." $Cyan
    
    $commands = @(
        "mkdir -p /root/stagingmvs",
        "mkdir -p /root/stagingmvs/nginx",
        "mkdir -p /root/stagingmvs/data",
        "mkdir -p /root/stagingmvs/logs",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/staging-volumes"
    )
    
    foreach ($cmd in $commands) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would execute: $cmd" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP $cmd
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Created: $cmd" $Green
            } else {
                Write-ColorOutput "❌ Failed: $cmd" $Red
            }
        }
    }
}

function Deploy-StagingConfiguration {
    Write-ColorOutput "📤 Deploying staging configuration..." $Cyan
    
    # Upload nginx configuration for staging
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would upload nginx-corrected.conf to /root/stagingmvs/nginx/" $Yellow
    } else {
        scp -i $SSHKeyPath "$LocalExportPath\nginx-corrected.conf" "$Username@${ServerIP}:/root/stagingmvs/nginx/nginx.conf"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Nginx configuration uploaded to staging" $Green
        } else {
            Write-ColorOutput "❌ Failed to upload nginx configuration" $Red
            return $false
        }
    }
    
    # Upload staging docker-compose
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would upload docker-compose-staging.yml to /root/stagingmvs/" $Yellow
    } else {
        scp -i $SSHKeyPath "$LocalExportPath\docker-compose-staging.yml" "$Username@${ServerIP}:/root/stagingmvs/docker-compose.yml"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Staging docker-compose uploaded" $Green
        } else {
            Write-ColorOutput "❌ Failed to upload staging docker-compose" $Red
            return $false
        }
    }
    
    return $true
}

function Deploy-DockerImages {
    Write-ColorOutput "🐳 Deploying Docker images to attached storage..." $Cyan
    
    # Get list of available Docker image files
    $imageFiles = Get-ChildItem -Path $LocalExportPath -Filter "*.tar.zip" | Sort-Object Name
    
    if ($imageFiles.Count -eq 0) {
        Write-ColorOutput "⚠️ No Docker image files found in $LocalExportPath" $Yellow
        return $true
    }
    
    foreach ($imageFile in $imageFiles) {
        Write-ColorOutput "📤 Uploading $($imageFile.Name) to attached storage..." $Cyan
        
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would upload $($imageFile.Name) to /mnt/volume_blr1_01/mvs-vr-project/docker-images/" $Yellow
        } else {
            # Check if file already exists
            $fileExists = ssh -i $SSHKeyPath $Username@$ServerIP "test -f /mnt/volume_blr1_01/mvs-vr-project/docker-images/$($imageFile.Name) && echo 'exists'"
            
            if ($fileExists -eq "exists") {
                Write-ColorOutput "⏭️ Skipping $($imageFile.Name) - already exists" $Yellow
            } else {
                # Upload to attached storage
                scp -i $SSHKeyPath $imageFile.FullName "$Username@${ServerIP}:/mnt/volume_blr1_01/mvs-vr-project/docker-images/"
                if ($LASTEXITCODE -eq 0) {
                    Write-ColorOutput "✅ Uploaded $($imageFile.Name) to attached storage" $Green
                } else {
                    Write-ColorOutput "❌ Failed to upload $($imageFile.Name)" $Red
                    continue
                }
            }
            
            # Extract and load the image from attached storage
            $imageName = $imageFile.BaseName
            Write-ColorOutput "🔄 Loading Docker image: $imageName..." $Cyan
            ssh -i $SSHKeyPath $Username@$ServerIP "cd /mnt/volume_blr1_01/mvs-vr-project/docker-images && unzip -o $($imageFile.Name) && docker load < $imageName.tar && rm $imageName.tar"
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Loaded Docker image: $imageName" $Green
            } else {
                Write-ColorOutput "❌ Failed to load Docker image: $imageName" $Red
            }
        }
    }
    
    return $true
}

function Create-StagingEnvironment {
    Write-ColorOutput "🔧 Creating staging environment file..." $Cyan
    
    $stagingEnv = @"
# Staging Environment Variables
NODE_ENV=staging
ENVIRONMENT=staging
DOMAIN=stagingmvs.kanousai.com

# Supabase Configuration (Staging)
STAGING_SUPABASE_URL=https://your-staging-project.supabase.co
STAGING_SUPABASE_ANON_KEY=your_staging_supabase_anon_key
STAGING_SUPABASE_SERVICE_ROLE_KEY=your_staging_supabase_service_role_key
STAGING_SUPABASE_DB_HOST=db.your-staging-project.supabase.co
STAGING_SUPABASE_DB_PORT=5432
STAGING_SUPABASE_DB_NAME=postgres
STAGING_SUPABASE_DB_USER=postgres
STAGING_SUPABASE_DB_PASSWORD=your_staging_db_password

# Redis Configuration
REDIS_PASSWORD=staging_redis_password_123

# Directus Configuration
DIRECTUS_KEY=staging-directus-key-$(Get-Random)
DIRECTUS_SECRET=staging-directus-secret-$(Get-Random)
STAGING_ADMIN_EMAIL=<EMAIL>
STAGING_ADMIN_PASSWORD=staging_admin_123

# JWT Configuration
STAGING_JWT_SECRET=staging-jwt-secret-$(Get-Random)

# API Keys (staging/test keys)
STAGING_OPENAI_API_KEY=sk-test-your-staging-openai-key
STAGING_ANTHROPIC_API_KEY=sk-ant-test-your-staging-anthropic-key
"@

    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would create staging .env file" $Yellow
    } else {
        $stagingEnv | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/stagingmvs/.env"
        Write-ColorOutput "✅ Created staging .env file" $Green
        Write-ColorOutput "⚠️ Remember to update the environment variables with real values!" $Yellow
    }
}

function Start-StagingServices {
    Write-ColorOutput "🚀 Starting staging services..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would start staging services" $Yellow
    } else {
        # Stop any existing services first
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose down 2>/dev/null || echo 'No existing services to stop'"
        
        # Start staging services
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose up -d"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Staging services started" $Green
        } else {
            Write-ColorOutput "❌ Failed to start staging services" $Red
            Write-ColorOutput "📋 Check logs with: ssh -i $SSHKeyPath $Username@$ServerIP 'cd /root/stagingmvs && docker-compose logs'" $Yellow
        }
    }
}

function Test-StagingDeployment {
    Write-ColorOutput "🧪 Testing staging deployment..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test staging endpoints" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 30 seconds for services to start..." $Yellow
    Start-Sleep -Seconds 30
    
    $testUrls = @(
        "http://stagingmvs.kanousai.com/health",
        "http://stagingmvs.kanousai.com/api/health"
    )
    
    foreach ($url in $testUrls) {
        Write-ColorOutput "🔍 Testing $url..." $Cyan
        $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' $url"
        if ($response -eq "200") {
            Write-ColorOutput "✅ $url - OK" $Green
        } else {
            Write-ColorOutput "❌ $url - Failed (HTTP $response)" $Red
        }
    }
    
    # Test Docker services status
    Write-ColorOutput "🐳 Checking Docker services status..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose ps"
}

# Main execution
Write-ColorOutput "🚀 MVS-VR Staging-Only Deployment" $Cyan
Write-ColorOutput "=================================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test SSH connection
if (-not (Test-SSHConnection)) {
    Write-ColorOutput "❌ Cannot proceed without SSH connection" $Red
    exit 1
}

# Execute staging deployment steps
Setup-AttachedStorage
Create-StagingDirectories
if (-not (Deploy-StagingConfiguration)) {
    Write-ColorOutput "❌ Staging configuration deployment failed" $Red
    exit 1
}
Deploy-DockerImages
Create-StagingEnvironment
Start-StagingServices
Test-StagingDeployment

Write-ColorOutput "✅ Staging deployment completed!" $Green
Write-ColorOutput "📋 Next steps:" $Cyan
Write-ColorOutput "1. Configure DNS: stagingmvs.kanousai.com → **************" $Yellow
Write-ColorOutput "2. Update environment variables in /root/stagingmvs/.env" $Yellow
Write-ColorOutput "3. Test staging endpoints after DNS propagation" $Yellow
Write-ColorOutput "4. Access staging at: http://stagingmvs.kanousai.com" $Yellow
