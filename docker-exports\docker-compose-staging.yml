# MVS-VR Staging Environment - /root/stagingmvs/
version: '3.8'

services:
  # Staging API Gateway
  staging_api_gateway:
    image: mvs-vr/api-gateway:latest
    container_name: staging_api_gateway
    restart: always
    environment:
      - NODE_ENV=staging
      - NEXT_PUBLIC_SUPABASE_URL=${STAGING_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${STAGING_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${STAGING_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
      - DIRECTUS_URL=http://staging_directus:8055
      - DIRECTUS_SECRET=${DIRECTUS_SECRET:-directus-secret}
      - ENVIRONMENT=staging
      - DOMAIN=stagingmvs.kanousai.com
    volumes:
      - staging_api_data:/app/data
      - staging_api_logs:/app/logs
      - staging_api_uploads:/app/uploads
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis
      - staging_directus
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Staging Directus CMS
  staging_directus:
    image: directus/directus:latest
    container_name: staging_directus
    restart: always
    environment:
      - KEY=${DIRECTUS_KEY:-directus-staging-key}
      - SECRET=${DIRECTUS_SECRET:-directus-staging-secret}
      - DB_CLIENT=pg
      - DB_HOST=${STAGING_SUPABASE_DB_HOST}
      - DB_PORT=${STAGING_SUPABASE_DB_PORT:-5432}
      - DB_DATABASE=${STAGING_SUPABASE_DB_NAME}
      - DB_USER=${STAGING_SUPABASE_DB_USER}
      - DB_PASSWORD=${STAGING_SUPABASE_DB_PASSWORD}
      - CACHE_ENABLED=true
      - CACHE_STORE=redis
      - CACHE_REDIS=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
      - ADMIN_EMAIL=${STAGING_ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${STAGING_ADMIN_PASSWORD:-staging123}
      - PUBLIC_URL=http://stagingmvs.kanousai.com/admin
      - CORS_ENABLED=true
      - CORS_ORIGIN=http://stagingmvs.kanousai.com
    volumes:
      - staging_directus_uploads:/directus/uploads
      - staging_directus_extensions:/directus/extensions
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8055/server/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Staging Redis Cache
  staging_redis:
    image: redis:7-alpine
    container_name: staging_redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - staging_redis_data:/data
    networks:
      - mvs_staging_network
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD:-redis_password}', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # Staging Auth Service
  staging_auth_service:
    image: mvs-vr/auth-service:latest
    container_name: staging_auth_service
    restart: always
    environment:
      - NODE_ENV=staging
      - SUPABASE_URL=${STAGING_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${STAGING_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
      - JWT_SECRET=${STAGING_JWT_SECRET:-staging-jwt-secret}
    volumes:
      - staging_auth_data:/app/data
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis

  # Staging Asset Service
  staging_asset_service:
    image: mvs-vr/asset-service:latest
    container_name: staging_asset_service
    restart: always
    environment:
      - NODE_ENV=staging
      - SUPABASE_URL=${STAGING_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${STAGING_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
      - STORAGE_PATH=/app/uploads
    volumes:
      - staging_asset_uploads:/app/uploads
      - staging_asset_data:/app/data
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis

  # Staging Blueprint Service
  staging_blueprint_service:
    image: mvs-vr/blueprint-service:latest
    container_name: staging_blueprint_service
    restart: always
    environment:
      - NODE_ENV=staging
      - SUPABASE_URL=${STAGING_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${STAGING_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
    volumes:
      - staging_blueprint_data:/app/data
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis

  # Staging Analytics Service
  staging_analytics_service:
    image: mvs-vr/analytics-service:latest
    container_name: staging_analytics_service
    restart: always
    environment:
      - NODE_ENV=staging
      - SUPABASE_URL=${STAGING_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${STAGING_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
    volumes:
      - staging_analytics_data:/app/data
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis

  # Staging LLM Service
  staging_llm_service:
    image: mvs-vr/llm-service:latest
    container_name: staging_llm_service
    restart: always
    environment:
      - NODE_ENV=staging
      - OPENAI_API_KEY=${STAGING_OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${STAGING_ANTHROPIC_API_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
    volumes:
      - staging_llm_data:/app/data
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis

  # Staging Monitoring Service
  staging_monitoring_service:
    image: mvs-vr/monitoring-service:latest
    container_name: staging_monitoring_service
    restart: always
    environment:
      - NODE_ENV=staging
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@staging_redis:6379
    volumes:
      - staging_monitoring_data:/app/data
      - staging_monitoring_metrics:/app/metrics
    networks:
      - mvs_staging_network
    depends_on:
      - staging_redis

volumes:
  staging_api_data:
  staging_api_logs:
  staging_api_uploads:
  staging_directus_uploads:
  staging_directus_extensions:
  staging_redis_data:
  staging_auth_data:
  staging_asset_uploads:
  staging_asset_data:
  staging_blueprint_data:
  staging_analytics_data:
  staging_llm_data:
  staging_monitoring_data:
  staging_monitoring_metrics:

networks:
  mvs_staging_network:
    driver: bridge
