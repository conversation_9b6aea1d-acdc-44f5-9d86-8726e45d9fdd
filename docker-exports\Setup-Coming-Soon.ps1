# Setup Coming Soon Page and Turn Off Production
# This script puts a coming soon page on mvs.kanousai.com and stops production services

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root", 
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Stop-ProductionServices {
    Write-ColorOutput "🛑 Stopping production services..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would stop production services" $Yellow
        return
    }
    
    # Stop production services
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose down"
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Production services stopped" $Green
    } else {
        Write-ColorOutput "⚠️ Production services may not have been running" $Yellow
    }
}

function Create-ComingSoonNginx {
    Write-ColorOutput "📄 Creating coming soon nginx configuration..." $Cyan
    
    $comingSoonNginx = @"
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Upstream definitions for staging services only
    upstream staging_api_gateway {
        server staging_api_gateway:3000;
    }

    upstream staging_directus {
        server staging_directus:8055;
    }

    # PRODUCTION DOMAIN - mvs.kanousai.com (COMING SOON)
    server {
        listen 80;
        server_name mvs.kanousai.com;

        # Coming Soon Page
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Coming Soon</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center;
        }
        .container { 
            max-width: 600px; 
            background: white; 
            padding: 60px 40px; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.3); 
            text-align: center;
        }
        h1 { 
            color: #333; 
            font-size: 3em; 
            margin-bottom: 20px; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle { 
            color: #666; 
            font-size: 1.2em; 
            margin-bottom: 30px; 
        }
        .status { 
            background: #fff3cd; 
            color: #856404; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 30px 0; 
            border-left: 5px solid #ffc107; 
        }
        .features {
            text-align: left;
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            color: #666;
            line-height: 1.8;
        }
        .contact {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #4caf50;
        }
        .staging-link {
            display: inline-block;
            padding: 12px 24px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            transition: background 0.3s;
        }
        .staging-link:hover {
            background: #ff5252;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 MVS-VR</h1>
        <p class='subtitle'>Virtual Reality Content Management Platform</p>
        
        <div class='status'>
            <strong>🚧 Coming Soon</strong><br>
            We're putting the finishing touches on our production environment. 
            The MVS-VR platform will be available soon!
        </div>
        
        <div class='features'>
            <h3>🎯 What's Coming:</h3>
            <ul>
                <li>🏢 <strong>Vendor Portal</strong> - Manage VR content and client experiences</li>
                <li>👥 <strong>Client Access</strong> - Interactive VR experiences</li>
                <li>⚙️ <strong>Admin Dashboard</strong> - System management and analytics</li>
                <li>🔗 <strong>API Gateway</strong> - Seamless integrations</li>
                <li>📊 <strong>Real-time Analytics</strong> - Performance insights</li>
            </ul>
        </div>
        
        <div class='contact'>
            <strong>📧 Contact:</strong> <EMAIL><br>
            <strong>🌐 Domain:</strong> mvs.kanousai.com<br>
            <strong>📅 Expected Launch:</strong> Soon
        </div>
        
        <a href='http://stagingmvs.kanousai.com' class='staging-link'>
            🧪 View Staging Environment
        </a>
        
        <p style='color: #999; font-size: 0.9em; margin-top: 30px;'>
            Server Status: Online | Environment: Production Setup | Version: 2.0
        </p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }

        # Health check still available
        location /health {
            access_log off;
            return 200 "MVS-VR Production - Coming Soon
Status: Preparing for launch
Contact: <EMAIL>
Staging: http://stagingmvs.kanousai.com
";
            add_header Content-Type text/plain;
        }
    }

    # STAGING DOMAIN - stagingmvs.kanousai.com (ACTIVE)
    server {
        listen 80;
        server_name stagingmvs.kanousai.com;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Staging - Active
Environment: Staging
Domain: stagingmvs.kanousai.com
Server Path: /root/stagingmvs/
API: stagingmvs.kanousai.com/api
Admin: stagingmvs.kanousai.com/admin
Vendor: stagingmvs.kanousai.com/vendor
";
            add_header Content-Type text/plain;
        }

        # API Gateway - Staging
        location /api/ {
            proxy_pass http://staging_api_gateway/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Admin Panel - Staging
        location /admin/ {
            proxy_pass http://staging_directus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Vendor Portal - Staging
        location /vendor/ {
            proxy_pass http://staging_api_gateway/vendor/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Home Page - Staging
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Staging Environment</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 10px; }
        .subtitle { text-align: center; color: #666; margin-bottom: 30px; }
        .warning { background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #ffc107; }
        .status { background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #4caf50; }
        .login-options { display: flex; gap: 20px; margin: 30px 0; }
        .login-card { flex: 1; background: #f8f9fa; padding: 25px; border-radius: 10px; text-align: center; border: 2px solid #e9ecef; }
        .btn { display: inline-block; padding: 12px 24px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 5px; }
        .links { text-align: center; margin: 30px 0; }
        .links a { color: #ff6b6b; text-decoration: none; margin: 0 15px; }
        .environment { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; text-align: center; font-weight: bold; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 MVS-VR Staging</h1>
        <p class='subtitle'>Testing Environment</p>
        
        <div class='environment'>⚠️ STAGING ENVIRONMENT - FOR TESTING ONLY</div>
        
        <div class='warning'>
            <strong>⚠️ Warning:</strong> This is a staging environment for testing purposes only. Data may be reset without notice.
        </div>
        
        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🌐 Domain:</strong> stagingmvs.kanousai.com<br>
            <strong>📡 Server:</strong> ************** (/root/stagingmvs/)<br>
            <strong>🔗 Database:</strong> Staging Database Connected
        </div>
        
        <div class='login-options'>
            <div class='login-card'>
                <h3>🏢 Vendor Testing</h3>
                <p>Test vendor functionality and new features before production deployment.</p>
                <a href='/vendor/' class='btn'>Test Vendor Portal</a>
            </div>
            <div class='login-card'>
                <h3>👥 Client Testing</h3>
                <p>Test client experiences and validate functionality.</p>
                <a href='/api/client-portal' class='btn'>Test Client Portal</a>
            </div>
        </div>
        
        <div class='links'>
            <a href='/api/health'>API Status</a> |
            <a href='/admin/'>Admin Testing</a> |
            <a href='http://mvs.kanousai.com'>Production (Coming Soon)</a>
        </div>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # DEFAULT/FALLBACK - Handle IP access and unknown domains
    server {
        listen 80 default_server;
        server_name _;

        # Redirect IP access to staging for now
        location / {
            return 301 http://stagingmvs.kanousai.com$request_uri;
        }

        # Health check for IP access
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure
Production: mvs.kanousai.com (Coming Soon)
Staging: stagingmvs.kanousai.com (Active)
Contact: <EMAIL>
";
            add_header Content-Type text/plain;
        }
    }
}
"@

    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would create coming soon nginx configuration" $Yellow
    } else {
        # Create coming soon nginx config
        $comingSoonNginx | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/nginx-coming-soon.conf"
        Write-ColorOutput "✅ Coming soon nginx configuration created" $Green
    }
}

function Deploy-ComingSoonNginx {
    Write-ColorOutput "🌐 Deploying coming soon nginx..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would deploy coming soon nginx" $Yellow
        return
    }
    
    # Stop any existing nginx
    ssh -i $SSHKeyPath $Username@$ServerIP "docker stop \$(docker ps -q --filter ancestor=nginx) 2>/dev/null || echo 'No nginx containers running'"
    ssh -i $SSHKeyPath $Username@$ServerIP "docker rm \$(docker ps -aq --filter ancestor=nginx) 2>/dev/null || echo 'No nginx containers to remove'"
    
    # Start new nginx with coming soon page
    ssh -i $SSHKeyPath $Username@$ServerIP "docker run -d --name coming-soon-nginx --restart always -p 80:80 -v /root/nginx-coming-soon.conf:/etc/nginx/nginx.conf:ro --network stagingmvs_mvs_staging_network nginx:alpine"
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Coming soon nginx deployed" $Green
    } else {
        Write-ColorOutput "❌ Failed to deploy coming soon nginx" $Red
    }
}

function Test-ComingSoon {
    Write-ColorOutput "🧪 Testing coming soon setup..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test coming soon setup" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 10 seconds for nginx to start..." $Yellow
    Start-Sleep -Seconds 10
    
    # Test endpoints
    $testUrls = @(
        @{url="http://mvs.kanousai.com/"; name="Production Coming Soon"},
        @{url="http://mvs.kanousai.com/health"; name="Production Health"},
        @{url="http://stagingmvs.kanousai.com/health"; name="Staging Health"}
    )
    
    foreach ($test in $testUrls) {
        $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' --connect-timeout 5 $($test.url)"
        
        if ($response -eq "200") {
            Write-ColorOutput "✅ $($test.name) - OK (200)" $Green
        } else {
            Write-ColorOutput "❌ $($test.name) - HTTP $response" $Red
        }
    }
}

function Show-Summary {
    Write-ColorOutput "`n📋 Coming Soon Setup Complete!" $Cyan
    
    Write-ColorOutput "✅ Actions Completed:" $Green
    Write-ColorOutput "  • Production services stopped" $Cyan
    Write-ColorOutput "  • Coming soon page deployed on mvs.kanousai.com" $Cyan
    Write-ColorOutput "  • Staging environment remains active" $Cyan
    
    Write-ColorOutput "`n🌐 Current Status:" $Cyan
    Write-ColorOutput "  • mvs.kanousai.com → Coming Soon Page" $Cyan
    Write-ColorOutput "  • stagingmvs.kanousai.com → Active Staging" $Cyan
    
    Write-ColorOutput "`n🎯 Next Steps:" $Yellow
    Write-ColorOutput "1. Configure DNS A records" $Yellow
    Write-ColorOutput "2. Update staging environment variables" $Yellow
    Write-ColorOutput "3. Test staging endpoints" $Yellow
    Write-ColorOutput "4. Prepare production when ready" $Yellow
}

# Main execution
Write-ColorOutput "🚧 MVS-VR Coming Soon Setup" $Cyan
Write-ColorOutput "===========================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test SSH connection
Write-ColorOutput "🔍 Testing SSH connection..." $Cyan
ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput "✅ SSH connection successful" $Green
} else {
    Write-ColorOutput "❌ SSH connection failed" $Red
    exit 1
}

# Execute coming soon setup
Stop-ProductionServices
Create-ComingSoonNginx
Deploy-ComingSoonNginx
Test-ComingSoon
Show-Summary

Write-ColorOutput "`n✅ Coming soon setup completed!" $Green
