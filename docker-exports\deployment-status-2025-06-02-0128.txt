MVS-VR DigitalOcean Deployment Status Report
Generated: 06/02/2025 01:29:57
Server: 157.245.103.57
Domain: mvs.kanousai.com
Project Path: /home/<USER>/mvs-vr-deployment

=== TEST RESULTS ===
01:28:51 - ℹ️  INFO: Starting MVS-VR DigitalOcean Deployment Check...
01:28:51 - ℹ️  INFO: Server: 157.245.103.57 | Domain: mvs.kanousai.com | Auto-fix: True
01:28:51 - ℹ️  INFO: Testing SSH connection to 157.245.103.57...
01:28:53 - ❌ ERROR: Cannot establish SSH connection
01:28:53 - ℹ️  INFO: Checking Docker installation...
01:28:55 - ❌ ERROR: Docker not found
01:28:55 - ℹ️  INFO: Installing Docker...
01:28:56 - ℹ️  INFO: Executed: curl -fsSL https://get.docker.com -o get-docker.sh
01:28:58 - ℹ️  INFO: Executed: sh get-docker.sh
01:29:00 - ℹ️  INFO: Executed: systemctl start docker
01:29:01 - ℹ️  INFO: Executed: systemctl enable docker
01:29:03 - ℹ️  INFO: Executed: usermod -aG docker vectorax
01:29:10 - ❌ ERROR: Docker installation failed
01:29:10 - ℹ️  INFO: Checking project deployment...
01:29:11 - ⚠️  WARNING: Project directory not found
01:29:11 - ℹ️  INFO: Deploying MVS-VR project...
01:29:13 - ℹ️  INFO: Copying project files...
01:29:15 - ✅ SUCCESS: Project files copied successfully
01:29:15 - ℹ️  INFO: Checking Docker containers...
01:29:17 - ⚠️  WARNING: No containers found or not running
01:29:17 - ℹ️  INFO: Starting Docker containers...
01:29:32 - ✅ SUCCESS: Containers started successfully
01:29:32 - ℹ️  INFO: Testing port 80 accessibility...
01:29:33 - ⚠️  WARNING: Port 80 not accessible locally (HTTP code: vectorax@157.245.103.57: Permission denied (publickey).)
01:29:35 - ✅ SUCCESS: Port 80 accessible externally from 157.245.103.57
01:29:35 - ℹ️  INFO: Testing domain resolution for mvs.kanousai.com...
01:29:35 - ⚠️  WARNING: Domain mvs.kanousai.com resolves to 104.21.84.39, expected 157.245.103.57
01:29:35 - ℹ️  INFO: Testing Supabase connectivity...
01:29:37 - ⚠️  WARNING: Supabase connectivity issue (HTTP code: vectorax@157.245.103.57: Permission denied (publickey).)
01:29:37 - ℹ️  INFO: Testing health endpoints...
01:29:39 - ⚠️  WARNING: Main Health endpoint failed (HTTP code: vectorax@157.245.103.57: Permission denied (publickey).)
01:29:40 - ⚠️  WARNING: Main Page endpoint failed (HTTP code: vectorax@157.245.103.57: Permission denied (publickey).)
01:29:41 - ✅ SUCCESS: External Health endpoint working
01:29:42 - ✅ SUCCESS: External Main endpoint working
01:29:42 - ℹ️  INFO: Checking service logs for errors...
01:29:44 - ✅ SUCCESS: Service nginx logs look clean
01:29:45 - ✅ SUCCESS: Service redis logs look clean
01:29:47 - ✅ SUCCESS: Service auth-service logs look clean
01:29:49 - ✅ SUCCESS: Service api-gateway logs look clean
01:29:50 - ✅ SUCCESS: Service asset-service logs look clean
01:29:52 - ✅ SUCCESS: Service analytics-service logs look clean
01:29:52 - ℹ️  INFO: Checking system resources...
01:29:57 - ✅ SUCCESS: Docker containers resource usage retrieved
Docker Stats:
vectorax@157.245.103.57: Permission denied (publickey).
01:29:57 - ℹ️  INFO: Generating deployment report...

=== FIXES APPLIED ===
Installing Docker
Deploying project files
Starting Docker containers

=== SUMMARY ===
Total Tests: 45
Fixes Applied: 3

=== NEXT STEPS ===
1. Verify all services are running properly
2. Configure DNS if domain resolution failed
3. Monitor logs for any issues
4. Test from external networks

Report saved to: deployment-status-2025-06-02-0128.txt
