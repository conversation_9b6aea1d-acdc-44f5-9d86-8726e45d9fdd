# Deploy Production Final - Clean Setup
# This script removes staging and sets up clean production environment

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr"
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Execute-ServerCommand {
    param([string]$Command, [string]$Description)
    
    Write-ColorOutput "🔄 $Description..." $Cyan
    $result = ssh -i $SSHKeyPath $Username@$ServerIP $Command 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ $Description completed" $Green
        if ($result) { Write-ColorOutput "   Output: $result" $White }
    } else {
        Write-ColorOutput "⚠️ $Description - $result" $Yellow
    }
}

Write-ColorOutput "🚀 MVS-VR Production Final Deployment" $Cyan
Write-ColorOutput "=====================================" $Cyan

# Step 1: Clean up staging and old containers
Write-ColorOutput "`n🧹 Cleaning up staging and old containers..." $Cyan

Execute-ServerCommand "cd /root/stagingmvs && docker-compose down 2>/dev/null || echo 'No staging compose to stop'" "Stop staging services"

Execute-ServerCommand "docker rm -f staging_api_gateway staging_directus staging_llm_service staging_monitoring_service staging_auth_service staging_blueprint_service staging_asset_service staging_analytics_service staging_redis 2>/dev/null || echo 'Staging containers already removed'" "Remove staging containers"

Execute-ServerCommand "docker rm -f pedantic_zhukovsky mvs-vr-v2_redis_1 2>/dev/null || echo 'Old containers already removed'" "Remove old containers"

Execute-ServerCommand "docker stop emergency-nginx && docker rm emergency-nginx" "Remove emergency nginx"

# Step 2: Create production directory structure
Write-ColorOutput "`n📁 Creating production directory structure..." $Cyan

Execute-ServerCommand "mkdir -p /root/mvs/{nginx,data,logs}" "Create production directories"

# Step 3: Create production docker-compose
Write-ColorOutput "`n🐳 Creating production docker-compose..." $Cyan

$productionCompose = @'
version: '3.8'

services:
  mvs_redis:
    image: redis:7-alpine
    container_name: mvs_redis
    restart: always
    command: redis-server --requirepass production_redis_123
    volumes:
      - mvs_redis_data:/data
    networks:
      - mvs_network
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', 'production_redis_123', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mvs_redis_data:

networks:
  mvs_network:
    driver: bridge
'@

$productionCompose | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/mvs/docker-compose.yml"
Write-ColorOutput "✅ Production docker-compose created" $Green

# Step 4: Create production environment
Write-ColorOutput "`n⚙️ Creating production environment..." $Cyan

$productionEnv = @'
NODE_ENV=production
ENVIRONMENT=production
DOMAIN=mvs.kanousai.com
REDIS_PASSWORD=production_redis_123
'@

$productionEnv | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/mvs/.env"
Write-ColorOutput "✅ Production environment created" $Green

# Step 5: Create professional nginx configuration
Write-ColorOutput "`n🌐 Creating professional nginx configuration..." $Cyan

$nginxConfig = @'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name mvs.kanousai.com _;

        location / {
            return 200 '<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Coming Soon</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { 
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center;
        }
        .container { 
            max-width: 700px; 
            background: white; 
            padding: 60px 40px; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.3); 
            text-align: center;
        }
        h1 { 
            color: #333; 
            font-size: 3em; 
            margin-bottom: 20px; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle { color: #666; font-size: 1.2em; margin-bottom: 30px; }
        .status { 
            background: #fff3cd; 
            color: #856404; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 30px 0; 
            border-left: 5px solid #ffc107; 
        }
        .features {
            text-align: left;
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .contact-form {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 5px solid #4caf50;
            text-align: left;
        }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #333; font-weight: bold; }
        .form-group input, .form-group textarea {
            width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; 
            font-size: 14px; box-sizing: border-box;
        }
        .submit-btn {
            background: #4caf50; color: white; padding: 12px 30px; border: none; 
            border-radius: 5px; cursor: pointer; font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 MVS-VR</h1>
        <p class="subtitle">Virtual Reality Content Management Platform</p>
        
        <div class="status">
            <strong>🚧 Coming Soon</strong><br>
            We are putting the finishing touches on our production environment. 
            The MVS-VR platform will be available soon!
        </div>
        
        <div class="features">
            <h3>🎯 What is Coming:</h3>
            <ul>
                <li>🏢 <strong>Vendor Portal</strong> - Manage VR content and client experiences</li>
                <li>👥 <strong>Client Access</strong> - Interactive VR experiences</li>
                <li>⚙️ <strong>Admin Dashboard</strong> - System management and analytics</li>
                <li>🔗 <strong>API Gateway</strong> - Seamless integrations</li>
                <li>📊 <strong>Real-time Analytics</strong> - Performance insights</li>
                <li>🤖 <strong>AI-Powered Tools</strong> - Smart content generation</li>
            </ul>
        </div>
        
        <div class="contact-form">
            <h3>📧 Get Notified When We Launch</h3>
            <form action="mailto:<EMAIL>" method="post" enctype="text/plain">
                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="company">Company (Optional):</label>
                    <input type="text" id="company" name="company">
                </div>
                <div class="form-group">
                    <label for="interest">Interest:</label>
                    <textarea id="interest" name="interest" placeholder="Tell us about your VR content needs..."></textarea>
                </div>
                <button type="submit" class="submit-btn">📬 Notify Me</button>
            </form>
        </div>
        
        <p style="color: #999; font-size: 0.9em; margin-top: 30px;">
            Server Status: Online | Environment: Production | Version: 2.0<br>
            Domain: mvs.kanousai.com | Server: **************
        </p>
    </div>
</body>
</html>';
            add_header Content-Type text/html;
        }

        location /health {
            return 200 'MVS-VR Production - Online
Status: Coming Soon Page Active
Domain: mvs.kanousai.com
Server: **************
Contact: <EMAIL>
';
            add_header Content-Type text/plain;
        }
    }
}
'@

$nginxConfig | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/mvs/nginx.conf"
Write-ColorOutput "✅ Professional nginx configuration created" $Green

# Step 6: Start production services
Write-ColorOutput "`n🚀 Starting production services..." $Cyan

Execute-ServerCommand "cd /root/mvs && docker-compose up -d" "Start production Redis"

Execute-ServerCommand "docker run -d --name mvs-production-nginx --restart always -p 80:80 -v /root/mvs/nginx.conf:/etc/nginx/nginx.conf:ro nginx:alpine" "Start production nginx"

# Step 7: Clean up staging directory and unused resources
Write-ColorOutput "`n🧹 Final cleanup..." $Cyan

Execute-ServerCommand "rm -rf /root/stagingmvs" "Remove staging directory"

Execute-ServerCommand "docker system prune -f" "Clean up unused Docker resources"

# Step 8: Test everything
Write-ColorOutput "`n🧪 Testing production environment..." $Cyan

Write-ColorOutput "⏳ Waiting 10 seconds for services to start..." $Yellow
Start-Sleep -Seconds 10

Execute-ServerCommand "curl -s -o /dev/null -w '%{http_code}' http://localhost/" "Test home page"

Execute-ServerCommand "curl -s -o /dev/null -w '%{http_code}' http://localhost/health" "Test health endpoint"

# Step 9: Show final status
Write-ColorOutput "`n📊 Final Status..." $Cyan

Execute-ServerCommand "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'" "Show running containers"

Write-ColorOutput "`n✅ Production deployment completed!" $Green
Write-ColorOutput "🌐 Your site is now live at: http://mvs.kanousai.com" $Cyan
Write-ColorOutput "📧 Contact form routes to: <EMAIL>" $Cyan
Write-ColorOutput "🎯 Professional coming soon page with lead generation" $Cyan
Write-ColorOutput "🧹 Staging environment removed as requested" $Cyan
