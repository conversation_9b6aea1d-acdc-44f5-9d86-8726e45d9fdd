# MVS-VR Production Deployment Script
# This script deploys the production environment to /root/mvs/

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr",
    [string]$LocalExportPath = "C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\docker-exports",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Test-SSHConnection {
    Write-ColorOutput "🔍 Testing SSH connection to $ServerIP..." $Cyan
    
    ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ SSH connection successful" $Green
        return $true
    } else {
        Write-ColorOutput "❌ SSH connection failed" $Red
        return $false
    }
}

function Create-ProductionDirectories {
    Write-ColorOutput "📁 Creating production directory structure..." $Cyan
    
    $commands = @(
        "mkdir -p /root/mvs",
        "mkdir -p /root/mvs/nginx",
        "mkdir -p /root/mvs/data",
        "mkdir -p /root/mvs/logs",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/production-volumes"
    )
    
    foreach ($cmd in $commands) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would execute: $cmd" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP $cmd
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Created: $cmd" $Green
            } else {
                Write-ColorOutput "❌ Failed: $cmd" $Red
            }
        }
    }
}

function Deploy-ProductionConfiguration {
    Write-ColorOutput "📤 Deploying production configuration..." $Cyan
    
    # Upload nginx configuration for production
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would upload nginx-corrected.conf to /root/mvs/nginx/" $Yellow
    } else {
        scp -i $SSHKeyPath "$LocalExportPath\nginx-corrected.conf" "$Username@${ServerIP}:/root/mvs/nginx/nginx.conf"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Nginx configuration uploaded to production" $Green
        } else {
            Write-ColorOutput "❌ Failed to upload nginx configuration" $Red
            return $false
        }
    }
    
    # Upload production docker-compose
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would upload docker-compose-production.yml to /root/mvs/" $Yellow
    } else {
        scp -i $SSHKeyPath "$LocalExportPath\docker-compose-production.yml" "$Username@${ServerIP}:/root/mvs/docker-compose.yml"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Production docker-compose uploaded" $Green
        } else {
            Write-ColorOutput "❌ Failed to upload production docker-compose" $Red
            return $false
        }
    }
    
    return $true
}

function Create-ProductionEnvironment {
    Write-ColorOutput "🔧 Creating production environment file..." $Cyan
    
    $productionEnv = @"
# Production Environment Variables
NODE_ENV=production
ENVIRONMENT=production
DOMAIN=mvs.kanousai.com

# Supabase Configuration (Production)
NEXT_PUBLIC_SUPABASE_URL=https://your-production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_supabase_service_role_key
SUPABASE_DB_HOST=db.your-production-project.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
SUPABASE_DB_PASSWORD=your_production_db_password

# Redis Configuration
REDIS_PASSWORD=production_redis_password_$(Get-Random)

# Directus Configuration
DIRECTUS_KEY=production-directus-key-$(Get-Random)
DIRECTUS_SECRET=production-directus-secret-$(Get-Random)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=production_admin_$(Get-Random)

# JWT Configuration
JWT_SECRET=production-jwt-secret-$(Get-Random)

# API Keys (production keys)
OPENAI_API_KEY=sk-your-production-openai-key
ANTHROPIC_API_KEY=sk-ant-your-production-anthropic-key
"@

    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would create production .env file" $Yellow
    } else {
        $productionEnv | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/mvs/.env"
        Write-ColorOutput "✅ Created production .env file" $Green
        Write-ColorOutput "⚠️ Remember to update the environment variables with real production values!" $Yellow
    }
}

function Tag-DockerImagesForProduction {
    Write-ColorOutput "🏷️ Tagging Docker images for production..." $Cyan
    
    # Map of image files to proper tags for production
    $imageTags = @{
        "analytics-service-20250601-192616" = "mvs-vr/analytics-service:latest"
        "api-gateway-20250601-192616" = "mvs-vr/api-gateway:latest"
        "asset-service-20250601-192616" = "mvs-vr/asset-service:latest"
        "auth-service-20250601-192616" = "mvs-vr/auth-service:latest"
        "blueprint-service-20250601-192616" = "mvs-vr/blueprint-service:latest"
        "directus-20250601-192616" = "directus/directus:latest"
        "llm-service-20250601-192616" = "mvs-vr/llm-service:latest"
        "monitoring-service-20250601-192616" = "mvs-vr/monitoring-service:latest"
    }
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would tag Docker images for production" $Yellow
        return
    }
    
    # Check if images are already properly tagged from staging deployment
    $existingImages = ssh -i $SSHKeyPath $Username@$ServerIP "docker images --format '{{.Repository}}:{{.Tag}}' | grep 'mvs-vr/'"
    
    if ($existingImages) {
        Write-ColorOutput "✅ Docker images already tagged from staging deployment" $Green
        Write-ColorOutput "Available images: $existingImages" $Cyan
    } else {
        Write-ColorOutput "⚠️ No properly tagged images found. Run staging deployment first." $Yellow
    }
}

function Setup-ProductionNginx {
    Write-ColorOutput "🌐 Setting up production nginx..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would configure nginx for production" $Yellow
        return
    }
    
    # Stop staging nginx if running
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose stop mvs_nginx 2>/dev/null || echo 'Staging nginx not running'"
    
    # The nginx configuration handles both production and staging
    Write-ColorOutput "✅ Nginx will serve both production and staging from unified config" $Green
}

function Start-ProductionServices {
    Write-ColorOutput "🚀 Starting production services..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would start production services" $Yellow
    } else {
        # Stop any existing services first
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose down 2>/dev/null || echo 'No existing services to stop'"
        
        # Start production services
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose up -d"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Production services started" $Green
        } else {
            Write-ColorOutput "❌ Failed to start production services" $Red
            Write-ColorOutput "📋 Check logs with: ssh -i $SSHKeyPath $Username@$ServerIP 'cd /root/mvs && docker-compose logs'" $Yellow
        }
    }
}

function Test-ProductionDeployment {
    Write-ColorOutput "🧪 Testing production deployment..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test production endpoints" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 30 seconds for services to start..." $Yellow
    Start-Sleep -Seconds 30
    
    $testUrls = @(
        "http://mvs.kanousai.com/health",
        "http://mvs.kanousai.com/api/health"
    )
    
    foreach ($url in $testUrls) {
        Write-ColorOutput "🔍 Testing $url..." $Cyan
        $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' $url"
        if ($response -eq "200") {
            Write-ColorOutput "✅ $url - OK" $Green
        } else {
            Write-ColorOutput "❌ $url - Failed (HTTP $response)" $Red
        }
    }
    
    # Test Docker services status
    Write-ColorOutput "🐳 Checking production Docker services status..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose ps"
}

function Show-ProductionSummary {
    Write-ColorOutput "`n📋 Production Deployment Summary:" $Cyan
    
    Write-ColorOutput "✅ Production Environment Created:" $Green
    Write-ColorOutput "  • Directory: /root/mvs/" $Cyan
    Write-ColorOutput "  • Domain: mvs.kanousai.com" $Cyan
    Write-ColorOutput "  • Environment: Production" $Cyan
    
    Write-ColorOutput "`n🌐 Production URLs (after DNS configuration):" $Cyan
    Write-ColorOutput "  • Home Page: http://mvs.kanousai.com/" $Cyan
    Write-ColorOutput "  • API Gateway: http://mvs.kanousai.com/api/" $Cyan
    Write-ColorOutput "  • Admin Panel: http://mvs.kanousai.com/admin/" $Cyan
    Write-ColorOutput "  • Vendor Portal: http://mvs.kanousai.com/vendor/" $Cyan
    
    Write-ColorOutput "`n🎯 Required DNS Configuration:" $Yellow
    Write-ColorOutput "Add this A record to your domain:" $Yellow
    Write-ColorOutput "Type: A" $Yellow
    Write-ColorOutput "Name: mvs" $Yellow
    Write-ColorOutput "Value: **************" $Yellow
    Write-ColorOutput "TTL: 300" $Yellow
    
    Write-ColorOutput "`n📝 Next Steps:" $Yellow
    Write-ColorOutput "1. Configure DNS: mvs.kanousai.com → **************" $Yellow
    Write-ColorOutput "2. Update /root/mvs/.env with real production Supabase credentials" $Yellow
    Write-ColorOutput "3. Test all production endpoints after DNS propagation" $Yellow
    Write-ColorOutput "4. Configure SSL certificates for production" $Yellow
}

# Main execution
Write-ColorOutput "🚀 MVS-VR Production Deployment" $Cyan
Write-ColorOutput "===============================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test SSH connection
if (-not (Test-SSHConnection)) {
    Write-ColorOutput "❌ Cannot proceed without SSH connection" $Red
    exit 1
}

# Execute production deployment steps
Create-ProductionDirectories
if (-not (Deploy-ProductionConfiguration)) {
    Write-ColorOutput "❌ Production configuration deployment failed" $Red
    exit 1
}
Create-ProductionEnvironment
Tag-DockerImagesForProduction
Setup-ProductionNginx
Start-ProductionServices
Test-ProductionDeployment
Show-ProductionSummary

Write-ColorOutput "`n✅ Production deployment completed!" $Green
