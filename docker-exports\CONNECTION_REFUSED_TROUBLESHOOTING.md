# 🚨 Connection Refused Error - Troubleshooting Guide

## 🔍 **Error Analysis**

**Error**: `net::ERR_CONNECTION_REFUSED` on `mvs.kanousai.com`

**Meaning**: The browser cannot connect to the server on port 80. This indicates:
1. The nginx service is not running
2. Port 80 is not accessible
3. Server firewall is blocking connections
4. D<PERSON> is pointing to wrong IP

## 🛠️ **Immediate Fix Steps**

### **Step 1: Test Server Connectivity**
```bash
# Test if server is reachable
ping **************

# Test if port 80 is open
telnet ************** 80
# OR
curl -I http://**************
```

### **Step 2: SSH into Server and Check Status**
```bash
# SSH into server
ssh -i C:\Users\<USER>\mvs-vr root@**************

# Check if any web server is running
docker ps | grep nginx
docker ps | grep 80

# Check what's listening on port 80
netstat -tlnp | grep :80
# OR
ss -tlnp | grep :80
```

### **Step 3: Restart Nginx Service**
```bash
# Stop any existing nginx containers
docker stop $(docker ps -q --filter ancestor=nginx) 2>/dev/null
docker rm $(docker ps -aq --filter ancestor=nginx) 2>/dev/null

# Start a simple nginx container
docker run -d --name mvs-nginx --restart always -p 80:80 nginx:alpine

# Test locally
curl http://localhost
```

### **Step 4: If Simple Nginx Works, Add Configuration**
```bash
# Create basic coming soon page
cat > /tmp/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head><title>MVS-VR - Coming Soon</title></head>
<body>
<h1>🚀 MVS-VR Coming Soon</h1>
<p>Virtual Reality Content Management Platform</p>
<p>Contact: <EMAIL></p>
</body>
</html>
EOF

# Copy to nginx container
docker cp /tmp/index.html mvs-nginx:/usr/share/nginx/html/index.html

# Test again
curl http://localhost
```

## 🔧 **Alternative Quick Fix**

If Docker is having issues, try a simple Python web server:

```bash
# Create simple coming soon page
mkdir -p /tmp/webroot
cat > /tmp/webroot/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head><title>MVS-VR - Coming Soon</title></head>
<body style="font-family: Arial; text-align: center; padding: 50px;">
<h1>🚀 MVS-VR</h1>
<h2>Coming Soon</h2>
<p>Virtual Reality Content Management Platform</p>
<p>We're preparing something amazing!</p>
<p>Contact: <EMAIL></p>
</body>
</html>
EOF

# Start simple web server
cd /tmp/webroot
python3 -m http.server 80 &

# Test
curl http://localhost
```

## 🌐 **DNS Verification**

Make sure DNS is still pointing correctly:

```bash
# Check DNS resolution
nslookup mvs.kanousai.com
dig mvs.kanousai.com

# Should return: **************
```

## 🔍 **Diagnostic Commands**

Run these commands on the server to diagnose:

```bash
# Check server resources
df -h
free -h
docker system df

# Check Docker daemon
systemctl status docker
docker version

# Check network connectivity
ip addr show
iptables -L

# Check logs
docker logs $(docker ps -q) 2>/dev/null
journalctl -u docker --since "1 hour ago"
```

## 🚨 **Emergency Recovery**

If nothing else works, here's a minimal recovery:

```bash
# Kill everything on port 80
sudo fuser -k 80/tcp

# Start minimal web server
echo "MVS-VR Coming Soon - Server Online" > /tmp/index.html
cd /tmp
python3 -m http.server 80 &

# Or use netcat
while true; do echo -e "HTTP/1.1 200 OK\n\nMVS-VR Coming Soon" | nc -l -p 80; done &
```

## 📋 **Manual Execution Steps**

**Execute these commands one by one:**

1. **Test connectivity**:
   ```bash
   ping **************
   ```

2. **SSH into server**:
   ```bash
   ssh -i C:\Users\<USER>\mvs-vr root@**************
   ```

3. **Check what's running**:
   ```bash
   docker ps
   netstat -tlnp | grep :80
   ```

4. **Start simple nginx**:
   ```bash
   docker run -d --name emergency-nginx -p 80:80 nginx:alpine
   ```

5. **Test locally**:
   ```bash
   curl http://localhost
   ```

6. **Test externally**:
   ```bash
   curl http://**************
   ```

## 🎯 **Expected Results**

After fixing, you should see:
- `ping **************` → Success
- `curl http://**************` → HTTP 200 OK
- `curl http://mvs.kanousai.com` → HTTP 200 OK
- Browser access to `mvs.kanousai.com` → Working page

## 📞 **If Still Not Working**

1. **Check server provider dashboard** - Server might be down
2. **Verify DNS propagation** - Use online DNS checkers
3. **Check firewall rules** - Port 80 might be blocked
4. **Review server logs** - Look for error messages
5. **Contact server provider** - There might be infrastructure issues

## ⚡ **Quick Test Commands**

Copy and paste these commands to test:

```bash
# Test 1: Server reachability
ping -c 4 **************

# Test 2: Port 80 accessibility  
curl -I --connect-timeout 10 http://**************

# Test 3: Domain resolution
nslookup mvs.kanousai.com

# Test 4: Domain accessibility
curl -I --connect-timeout 10 http://mvs.kanousai.com
```

The connection refused error is fixable - it's likely just the nginx container that stopped running.
