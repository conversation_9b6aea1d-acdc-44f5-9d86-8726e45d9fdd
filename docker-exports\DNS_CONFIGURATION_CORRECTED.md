# 🌐 Corrected DNS Configuration for MVS-VR

## 📋 **Required DNS Records**

### **Simplified DNS Structure**
You need to configure these DNS records for your domain `kanousai.com`:

| Domain | Type | Name | Value | TTL | Purpose |
|--------|------|------|-------|-----|---------|
| **Root Domain** | A | `@` or blank | `**************` | 300 | kanousai.com (separate setup) |
| **Production** | A | `mvs` | `**************` | 300 | mvs.kanousai.com → /root/mvs/ |
| **Staging** | A | `stagingmvs` | `**************` | 300 | stagingmvs.kanousai.com → /root/stagingmvs/ |

### **Path-Based Routing (No Additional DNS Needed)**
- **API Gateway**: `mvs.kanousai.com/api` (under main domain)
- **Admin Panel**: `mvs.kanousai.com/admin` (under main domain)
- **Vendor Portal**: `mvs.kanousai.com/vendor` (under main domain)

## 🔧 **How to Configure DNS**

### **Step 1: Add A Records**

#### **For Most DNS Providers (Cloudflare, Namecheap, GoDaddy, etc.)**

1. **Log into your DNS provider dashboard**
2. **Navigate to DNS Management** for `kanousai.com`
3. **Add these A records:**

```
Type: A    Name: @           Value: **************    (kanousai.com root)
Type: A    Name: mvs         Value: **************    (Production)
Type: A    Name: stagingmvs  Value: **************    (Staging)
```

#### **For Cloudflare (Recommended)**

1. **Add A Records:**
   - `kanousai.com` → `**************` (Proxied: ☁️)
   - `mvs.kanousai.com` → `**************` (Proxied: ☁️)
   - `stagingmvs.kanousai.com` → `**************` (DNS Only: 🌐)

2. **SSL/TLS Settings:**
   - Set to "Full" or "Full (Strict)"
   - Enable "Always Use HTTPS"

### **Step 2: Verify DNS Configuration**

#### **Check DNS Propagation**
```bash
# Check each domain
nslookup kanousai.com
nslookup mvs.kanousai.com
nslookup stagingmvs.kanousai.com

# Or use dig
dig kanousai.com
dig mvs.kanousai.com
dig stagingmvs.kanousai.com
```

#### **Online DNS Checkers**
- https://dnschecker.org/
- https://www.whatsmydns.net/
- https://dns.google/ (Google DNS)

#### **Expected Results**
All domains should resolve to: `**************`

## 🧪 **Test Your Configuration**

Once DNS is configured, test each domain and path:

### **1. Root Domain (Future Setup)**
```bash
curl -I http://kanousai.com/
# Expected: Will be configured separately
```

### **2. Production Domain**
```bash
# Home page with vendor/client login options
curl -I http://mvs.kanousai.com/
# Expected: HTTP/1.1 200 OK

# API Gateway
curl -I http://mvs.kanousai.com/api/health
# Expected: HTTP/1.1 200 OK

# Admin Panel
curl -I http://mvs.kanousai.com/admin/
# Expected: HTTP/1.1 200 OK

# Vendor Portal
curl -I http://mvs.kanousai.com/vendor/
# Expected: HTTP/1.1 200 OK
```

### **3. Staging Domain**
```bash
# Staging home page
curl -I http://stagingmvs.kanousai.com/
# Expected: HTTP/1.1 200 OK

# Staging API
curl -I http://stagingmvs.kanousai.com/api/health
# Expected: HTTP/1.1 200 OK

# Staging Admin
curl -I http://stagingmvs.kanousai.com/admin/
# Expected: HTTP/1.1 200 OK

# Staging Vendor Portal
curl -I http://stagingmvs.kanousai.com/vendor/
# Expected: HTTP/1.1 200 OK
```

## 🏗️ **Server Directory Structure**

### **After Deployment**
```
/root/
├── mvs/ (Production - mvs.kanousai.com)
│   ├── docker-compose.yml
│   ├── .env
│   ├── nginx/
│   │   └── nginx.conf
│   ├── data/
│   └── logs/
└── stagingmvs/ (Staging - stagingmvs.kanousai.com)
    ├── docker-compose.yml
    ├── .env
    ├── nginx/
    │   └── nginx.conf
    ├── data/
    └── logs/
```

## 🎯 **User Experience Flow**

### **Production Flow (mvs.kanousai.com)**
```
User visits mvs.kanousai.com
    ↓
Sees home page with login options:
├── Vendor Login → /vendor/
└── Client Access → /api/client-portal
    ↓
System Admin → /admin/
API Documentation → /api/
```

### **Staging Flow (stagingmvs.kanousai.com)**
```
Developer visits stagingmvs.kanousai.com
    ↓
Sees staging warning + login options:
├── Test Vendor Portal → /vendor/
└── Test Client Portal → /api/client-portal
    ↓
Test Admin Panel → /admin/
Test API → /api/
```

## 📋 **DNS Configuration Checklist**

- [ ] **Root Domain**: `kanousai.com` → `**************`
- [ ] **Production**: `mvs.kanousai.com` → `**************`
- [ ] **Staging**: `stagingmvs.kanousai.com` → `**************`
- [ ] **TTL Set**: 300 seconds (5 minutes) for faster updates
- [ ] **Propagation Check**: All domains resolve correctly
- [ ] **HTTP Test**: All domains return 200 OK
- [ ] **Path Test**: All paths (/api, /admin, /vendor) work correctly
- [ ] **Browser Test**: All pages load correctly

## 🚨 **Important Changes from Previous Setup**

### **What's Different:**
1. ✅ **Simplified DNS**: Only 3 A records instead of 4+ subdomains
2. ✅ **Path-Based Routing**: `/api`, `/admin`, `/vendor` instead of subdomains
3. ✅ **Correct Directory Structure**: `/root/mvs/` and `/root/stagingmvs/`
4. ✅ **Unified Home Page**: Vendor/client login options on same page
5. ✅ **Environment Separation**: Clear production vs staging distinction

### **What's Removed:**
- ❌ `api.mvs.kanousai.com` (now `/api`)
- ❌ `admin.mvs.kanousai.com` (now `/admin`)
- ❌ Complex subdomain routing
- ❌ `/home/<USER>/` paths

## 🕐 **DNS Propagation Timeline**

- **Local DNS**: 5-15 minutes
- **ISP DNS**: 30 minutes - 2 hours  
- **Global Propagation**: 2-24 hours
- **Full Propagation**: Up to 48 hours

## 📞 **Domain Host Instructions**

### **For Your Domain Registrar/DNS Provider:**

**Tell them to add these exact DNS records:**

```
Domain: kanousai.com

Record 1:
Type: A
Name: @ (or leave blank for root domain)
Value: **************
TTL: 300

Record 2:
Type: A
Name: mvs
Value: **************
TTL: 300

Record 3:
Type: A
Name: stagingmvs
Value: **************
TTL: 300
```

**Optional (for www redirect):**
```
Record 4:
Type: CNAME
Name: www
Value: kanousai.com
TTL: 300
```

## ✅ **Verification Commands**

After DNS configuration, run these commands to verify:

```bash
# Test DNS resolution
nslookup mvs.kanousai.com
nslookup stagingmvs.kanousai.com

# Test HTTP responses
curl -I http://mvs.kanousai.com/
curl -I http://mvs.kanousai.com/api/health
curl -I http://stagingmvs.kanousai.com/
curl -I http://stagingmvs.kanousai.com/api/health
```

All should return `HTTP/1.1 200 OK` once deployment is complete.

## 🎯 **Expected Results**

### **After DNS + Deployment:**
- `kanousai.com` → Root domain (separate setup)
- `mvs.kanousai.com` → Production with vendor/client login
- `mvs.kanousai.com/api` → Production API Gateway
- `mvs.kanousai.com/admin` → Production Admin Panel
- `mvs.kanousai.com/vendor` → Production Vendor Portal
- `stagingmvs.kanousai.com` → Staging environment with all features

This configuration is much simpler, more maintainable, and aligns with your requirements!
