# MVS-VR Corrected Server Structure Deployment Script
# This script implements the corrected directory structure and domain configuration

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\.ssh\mvs-vr",
    [string]$LocalExportPath = "C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\docker-exports",
    [switch]$ProductionOnly = $false,
    [switch]$StagingOnly = $false,
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Test-SSHConnection {
    Write-ColorOutput "🔍 Testing SSH connection to $ServerIP..." $Cyan
    
    $testResult = ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'"
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ SSH connection successful" $Green
        return $true
    } else {
        Write-ColorOutput "❌ SSH connection failed" $Red
        return $false
    }
}

function Create-ServerDirectories {
    Write-ColorOutput "📁 Creating corrected directory structure on server..." $Cyan
    
    $commands = @(
        "mkdir -p /root/mvs",
        "mkdir -p /root/stagingmvs",
        "mkdir -p /root/mvs/nginx",
        "mkdir -p /root/stagingmvs/nginx",
        "mkdir -p /root/mvs/data",
        "mkdir -p /root/stagingmvs/data",
        "mkdir -p /root/mvs/logs",
        "mkdir -p /root/stagingmvs/logs"
    )
    
    foreach ($cmd in $commands) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would execute: $cmd" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP $cmd
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Created: $cmd" $Green
            } else {
                Write-ColorOutput "❌ Failed: $cmd" $Red
            }
        }
    }
}

function Backup-ExistingDeployment {
    Write-ColorOutput "💾 Backing up existing deployment..." $Cyan
    
    $backupCommands = @(
        "if [ -d '/home/<USER>/mvs-vr-deployment' ]; then cp -r /home/<USER>/mvs-vr-deployment /root/backup-$(date +%Y%m%d-%H%M%S); fi",
        "if [ -d '/root/vectorax' ]; then cp -r /root/vectorax /root/backup-vectorax-$(date +%Y%m%d-%H%M%S); fi"
    )
    
    foreach ($cmd in $backupCommands) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would execute: $cmd" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP $cmd
        }
    }
    
    Write-ColorOutput "✅ Backup completed" $Green
}

function Deploy-ConfigurationFiles {
    Write-ColorOutput "📤 Deploying configuration files..." $Cyan
    
    # Upload nginx configuration
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would upload nginx-corrected.conf to /root/mvs/nginx/" $Yellow
    } else {
        scp -i $SSHKeyPath "$LocalExportPath\nginx-corrected.conf" "$Username@${ServerIP}:/root/mvs/nginx/nginx.conf"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Nginx configuration uploaded" $Green
        } else {
            Write-ColorOutput "❌ Failed to upload nginx configuration" $Red
            return $false
        }
    }
    
    # Upload production docker-compose
    if (-not $StagingOnly) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would upload docker-compose-production.yml to /root/mvs/" $Yellow
        } else {
            scp -i $SSHKeyPath "$LocalExportPath\docker-compose-production.yml" "$Username@${ServerIP}:/root/mvs/docker-compose.yml"
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Production docker-compose uploaded" $Green
            } else {
                Write-ColorOutput "❌ Failed to upload production docker-compose" $Red
                return $false
            }
        }
    }
    
    # Upload staging docker-compose
    if (-not $ProductionOnly) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would upload docker-compose-staging.yml to /root/stagingmvs/" $Yellow
        } else {
            scp -i $SSHKeyPath "$LocalExportPath\docker-compose-staging.yml" "$Username@${ServerIP}:/root/stagingmvs/docker-compose.yml"
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Staging docker-compose uploaded" $Green
            } else {
                Write-ColorOutput "❌ Failed to upload staging docker-compose" $Red
                return $false
            }
        }
    }
    
    return $true
}

function Setup-AttachedStorage {
    Write-ColorOutput "💾 Setting up attached storage..." $Cyan

    $storageCommands = @(
        "mkdir -p /mnt/volume_blr1_01",
        "mount -o discard,defaults /dev/disk/by-id/scsi-0DO_Volume_volume-blr1-01 /mnt/volume_blr1_01",
        "echo '/dev/disk/by-id/scsi-0DO_Volume_volume-blr1-01 /mnt/volume_blr1_01 ext4 defaults,nofail,discard 0 0' | tee -a /etc/fstab",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/docker-images",
        "mkdir -p /mnt/volume_blr1_01/mvs-vr-project/backups",
        "chown -R root:root /mnt/volume_blr1_01/mvs-vr-project"
    )

    foreach ($cmd in $storageCommands) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would execute: $cmd" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP $cmd
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Storage setup: $cmd" $Green
            } else {
                Write-ColorOutput "❌ Storage setup failed: $cmd" $Red
            }
        }
    }

    # Verify storage is mounted
    if (-not $DryRun) {
        $mountCheck = ssh -i $SSHKeyPath $Username@$ServerIP "df -h | grep volume_blr1_01"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Attached storage mounted successfully" $Green
            Write-ColorOutput "Storage info: $mountCheck" $Cyan
        } else {
            Write-ColorOutput "❌ Attached storage mount verification failed" $Red
        }
    }
}

function Deploy-DockerImages {
    Write-ColorOutput "🐳 Deploying Docker images to attached storage..." $Cyan

    # Get list of available Docker image files
    $imageFiles = Get-ChildItem -Path $LocalExportPath -Filter "*.tar.zip" | Sort-Object Name

    if ($imageFiles.Count -eq 0) {
        Write-ColorOutput "⚠️ No Docker image files found in $LocalExportPath" $Yellow
        return $true
    }

    foreach ($imageFile in $imageFiles) {
        Write-ColorOutput "📤 Uploading $($imageFile.Name) to attached storage..." $Cyan

        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would upload $($imageFile.Name) to /mnt/volume_blr1_01/mvs-vr-project/docker-images/" $Yellow
        } else {
            # Upload to attached storage instead of /root/
            scp -i $SSHKeyPath $imageFile.FullName "$Username@${ServerIP}:/mnt/volume_blr1_01/mvs-vr-project/docker-images/"
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Uploaded $($imageFile.Name) to attached storage" $Green

                # Extract and load the image from attached storage
                $imageName = $imageFile.BaseName
                ssh -i $SSHKeyPath $Username@$ServerIP "cd /mnt/volume_blr1_01/mvs-vr-project/docker-images && unzip -o $($imageFile.Name) && docker load < $imageName.tar && rm $imageName.tar"

                if ($LASTEXITCODE -eq 0) {
                    Write-ColorOutput "✅ Loaded Docker image: $imageName" $Green
                } else {
                    Write-ColorOutput "❌ Failed to load Docker image: $imageName" $Red
                }
            } else {
                Write-ColorOutput "❌ Failed to upload $($imageFile.Name)" $Red
            }
        }
    }

    return $true
}

function Create-EnvironmentFiles {
    Write-ColorOutput "🔧 Creating environment files..." $Cyan
    
    $productionEnv = @"
# Production Environment Variables
NODE_ENV=production
ENVIRONMENT=production
DOMAIN=mvs.kanousai.com

# Supabase Configuration (Production)
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_supabase_service_role_key
SUPABASE_DB_HOST=your_production_db_host
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=your_production_db_name
SUPABASE_DB_USER=your_production_db_user
SUPABASE_DB_PASSWORD=your_production_db_password

# Redis Configuration
REDIS_PASSWORD=your_redis_password

# Directus Configuration
DIRECTUS_KEY=your_directus_key
DIRECTUS_SECRET=your_directus_secret
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_admin_password

# JWT Configuration
JWT_SECRET=your_jwt_secret

# API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
"@

    $stagingEnv = @"
# Staging Environment Variables
NODE_ENV=staging
ENVIRONMENT=staging
DOMAIN=stagingmvs.kanousai.com

# Supabase Configuration (Staging)
STAGING_SUPABASE_URL=your_staging_supabase_url
STAGING_SUPABASE_ANON_KEY=your_staging_supabase_anon_key
STAGING_SUPABASE_SERVICE_ROLE_KEY=your_staging_supabase_service_role_key
STAGING_SUPABASE_DB_HOST=your_staging_db_host
STAGING_SUPABASE_DB_PORT=5432
STAGING_SUPABASE_DB_NAME=your_staging_db_name
STAGING_SUPABASE_DB_USER=your_staging_db_user
STAGING_SUPABASE_DB_PASSWORD=your_staging_db_password

# Redis Configuration
REDIS_PASSWORD=your_redis_password

# Directus Configuration
DIRECTUS_KEY=your_staging_directus_key
DIRECTUS_SECRET=your_staging_directus_secret
STAGING_ADMIN_EMAIL=<EMAIL>
STAGING_ADMIN_PASSWORD=your_staging_admin_password

# JWT Configuration
STAGING_JWT_SECRET=your_staging_jwt_secret

# API Keys (can be same as production or separate)
STAGING_OPENAI_API_KEY=your_staging_openai_api_key
STAGING_ANTHROPIC_API_KEY=your_staging_anthropic_api_key
"@

    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would create .env files" $Yellow
    } else {
        # Create production .env
        if (-not $StagingOnly) {
            $productionEnv | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/mvs/.env"
            Write-ColorOutput "✅ Created production .env file" $Green
        }
        
        # Create staging .env
        if (-not $ProductionOnly) {
            $stagingEnv | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/stagingmvs/.env"
            Write-ColorOutput "✅ Created staging .env file" $Green
        }
    }
}

function Start-Services {
    Write-ColorOutput "🚀 Starting services..." $Cyan
    
    if (-not $StagingOnly) {
        Write-ColorOutput "Starting production services..." $Cyan
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would start production services" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose up -d"
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Production services started" $Green
            } else {
                Write-ColorOutput "❌ Failed to start production services" $Red
            }
        }
    }
    
    if (-not $ProductionOnly) {
        Write-ColorOutput "Starting staging services..." $Cyan
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would start staging services" $Yellow
        } else {
            ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose up -d"
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Staging services started" $Green
            } else {
                Write-ColorOutput "❌ Failed to start staging services" $Red
            }
        }
    }
}

function Test-Deployment {
    Write-ColorOutput "🧪 Testing deployment..." $Cyan
    
    Start-Sleep -Seconds 30  # Wait for services to start
    
    $testUrls = @()
    if (-not $StagingOnly) {
        $testUrls += "http://mvs.kanousai.com/health"
        $testUrls += "http://mvs.kanousai.com/api/health"
    }
    if (-not $ProductionOnly) {
        $testUrls += "http://stagingmvs.kanousai.com/health"
        $testUrls += "http://stagingmvs.kanousai.com/api/health"
    }
    
    foreach ($url in $testUrls) {
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would test $url" $Yellow
        } else {
            $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' $url"
            if ($response -eq "200") {
                Write-ColorOutput "✅ $url - OK" $Green
            } else {
                Write-ColorOutput "❌ $url - Failed (HTTP $response)" $Red
            }
        }
    }
}

# Main execution
Write-ColorOutput "🚀 MVS-VR Corrected Structure Deployment" $Cyan
Write-ColorOutput "========================================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test SSH connection
if (-not (Test-SSHConnection)) {
    Write-ColorOutput "❌ Cannot proceed without SSH connection" $Red
    exit 1
}

# Execute deployment steps
Backup-ExistingDeployment
Setup-AttachedStorage
Create-ServerDirectories
if (-not (Deploy-ConfigurationFiles)) {
    Write-ColorOutput "❌ Configuration deployment failed" $Red
    exit 1
}
Deploy-DockerImages
Create-EnvironmentFiles
Start-Services
Test-Deployment

Write-ColorOutput "✅ Deployment completed!" $Green
Write-ColorOutput "📋 Next steps:" $Cyan
Write-ColorOutput "1. Configure DNS records (see DNS_CONFIGURATION_CORRECTED.md)" $Yellow
Write-ColorOutput "2. Update environment variables in .env files" $Yellow
Write-ColorOutput "3. Test all endpoints after DNS propagation" $Yellow
