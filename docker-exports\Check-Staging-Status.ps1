# Check Staging Status Script
# This script checks the status of the staging deployment

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr"
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Check-Infrastructure {
    Write-ColorOutput "🏗️ Checking Infrastructure..." $Cyan
    
    # Check attached storage
    Write-ColorOutput "💾 Attached Storage:" $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "df -h | grep volume_blr1_01"
    
    # Check directory structure
    Write-ColorOutput "📁 Directory Structure:" $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "ls -la /root/stagingmvs/"
    
    # Check Docker images
    Write-ColorOutput "🐳 Docker Images:" $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "docker images | grep mvs-vr"
}

function Check-Services {
    Write-ColorOutput "🔍 Checking Services Status..." $Cyan
    
    # Check Docker Compose status
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose ps"
    
    Write-ColorOutput "`n📊 Service Health Summary:" $Cyan
    $services = @("staging_redis", "staging_api_gateway", "staging_directus", "staging_auth_service", "staging_asset_service", "staging_blueprint_service", "staging_analytics_service", "staging_llm_service", "staging_monitoring_service")
    
    foreach ($service in $services) {
        $status = ssh -i $SSHKeyPath $Username@$ServerIP "docker inspect $service --format='{{.State.Status}}' 2>/dev/null || echo 'not found'"
        $health = ssh -i $SSHKeyPath $Username@$ServerIP "docker inspect $service --format='{{.State.Health.Status}}' 2>/dev/null || echo 'no health check'"
        
        if ($status -eq "running") {
            if ($health -eq "healthy") {
                Write-ColorOutput "✅ $service - Running & Healthy" $Green
            } elseif ($health -eq "no health check") {
                Write-ColorOutput "🟡 $service - Running (no health check)" $Yellow
            } else {
                Write-ColorOutput "⚠️ $service - Running but $health" $Yellow
            }
        } else {
            Write-ColorOutput "❌ $service - $status" $Red
        }
    }
}

function Check-Logs {
    Write-ColorOutput "`n📋 Recent Service Logs:" $Cyan
    
    $services = @("staging_api_gateway", "staging_directus", "staging_redis")
    
    foreach ($service in $services) {
        Write-ColorOutput "`n--- $service logs (last 5 lines) ---" $Cyan
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose logs --tail=5 $service 2>/dev/null || echo 'No logs available'"
    }
}

function Test-Endpoints {
    Write-ColorOutput "`n🌐 Testing Endpoints..." $Cyan
    
    $endpoints = @(
        @{url="http://stagingmvs.kanousai.com/health"; name="Main Health Check"},
        @{url="http://stagingmvs.kanousai.com/"; name="Home Page"},
        @{url="http://stagingmvs.kanousai.com/api/health"; name="API Health Check"},
        @{url="http://stagingmvs.kanousai.com/admin/"; name="Admin Panel"},
        @{url="http://stagingmvs.kanousai.com/vendor/"; name="Vendor Portal"}
    )
    
    foreach ($endpoint in $endpoints) {
        $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' --connect-timeout 5 $($endpoint.url)"
        
        switch ($response) {
            "200" { Write-ColorOutput "✅ $($endpoint.name) - OK (200)" $Green }
            "301" { Write-ColorOutput "🔄 $($endpoint.name) - Redirect (301)" $Yellow }
            "302" { Write-ColorOutput "🔄 $($endpoint.name) - Redirect (302)" $Yellow }
            "404" { Write-ColorOutput "❌ $($endpoint.name) - Not Found (404)" $Red }
            "500" { Write-ColorOutput "❌ $($endpoint.name) - Server Error (500)" $Red }
            "502" { Write-ColorOutput "❌ $($endpoint.name) - Bad Gateway (502)" $Red }
            "503" { Write-ColorOutput "❌ $($endpoint.name) - Service Unavailable (503)" $Red }
            default { Write-ColorOutput "⚠️ $($endpoint.name) - HTTP $response" $Yellow }
        }
    }
}

function Check-DNS {
    Write-ColorOutput "`n🌐 DNS Configuration Check..." $Cyan
    
    # Check if DNS is configured
    $dnsResult = nslookup stagingmvs.kanousai.com 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ DNS Resolution: stagingmvs.kanousai.com resolves" $Green
        Write-ColorOutput "$dnsResult" $Cyan
    } else {
        Write-ColorOutput "❌ DNS Resolution: stagingmvs.kanousai.com does not resolve" $Red
        Write-ColorOutput "⚠️ You need to configure DNS: stagingmvs.kanousai.com → **************" $Yellow
    }
}

function Show-NextSteps {
    Write-ColorOutput "`n📋 Next Steps & Recommendations:" $Cyan
    
    # Check if services are running
    $runningServices = ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose ps --services --filter status=running | wc -l"
    $totalServices = ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose ps --services | wc -l"
    
    Write-ColorOutput "📊 Services Status: $runningServices/$totalServices running" $Cyan
    
    if ($runningServices -lt $totalServices) {
        Write-ColorOutput "🔧 Some services are not running. Recommended actions:" $Yellow
        Write-ColorOutput "1. Check service logs: ssh -i $SSHKeyPath $Username@$ServerIP 'cd /root/stagingmvs && docker-compose logs [service-name]'" $Yellow
        Write-ColorOutput "2. Restart services: ssh -i $SSHKeyPath $Username@$ServerIP 'cd /root/stagingmvs && docker-compose restart'" $Yellow
        Write-ColorOutput "3. Update environment variables in /root/stagingmvs/.env with real Supabase credentials" $Yellow
    }
    
    Write-ColorOutput "`n🎯 Configuration Tasks:" $Yellow
    Write-ColorOutput "1. Configure DNS: stagingmvs.kanousai.com → **************" $Yellow
    Write-ColorOutput "2. Update /root/stagingmvs/.env with real Supabase credentials" $Yellow
    Write-ColorOutput "3. Test all endpoints after DNS propagation" $Yellow
    Write-ColorOutput "4. Configure SSL certificates (optional)" $Yellow
    
    Write-ColorOutput "`n🌐 Access URLs (after DNS configuration):" $Cyan
    Write-ColorOutput "• Home Page: http://stagingmvs.kanousai.com/" $Cyan
    Write-ColorOutput "• API Gateway: http://stagingmvs.kanousai.com/api/" $Cyan
    Write-ColorOutput "• Admin Panel: http://stagingmvs.kanousai.com/admin/" $Cyan
    Write-ColorOutput "• Vendor Portal: http://stagingmvs.kanousai.com/vendor/" $Cyan
}

# Main execution
Write-ColorOutput "🔍 MVS-VR Staging Status Check" $Cyan
Write-ColorOutput "==============================" $Cyan

# Test SSH connection
Write-ColorOutput "🔍 Testing SSH connection..." $Cyan
ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput "✅ SSH connection successful" $Green
} else {
    Write-ColorOutput "❌ SSH connection failed" $Red
    exit 1
}

# Run all checks
Check-Infrastructure
Check-Services
Check-Logs
Test-Endpoints
Check-DNS
Show-NextSteps

Write-ColorOutput "`n✅ Status check completed!" $Green
