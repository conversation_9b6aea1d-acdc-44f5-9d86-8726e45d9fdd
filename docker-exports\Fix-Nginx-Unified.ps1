# Fix Unified Nginx Configuration
# This script sets up a single nginx instance to serve both production and staging

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Stop-AllNginx {
    Write-ColorOutput "🛑 Stopping all nginx instances..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would stop all nginx containers" $Yellow
        return
    }
    
    # Stop nginx from both environments
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose stop mvs_nginx 2>/dev/null || echo 'Production nginx not running'"
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose stop staging_nginx 2>/dev/null || echo 'Staging nginx not running'"
    
    # Stop any standalone nginx containers
    ssh -i $SSHKeyPath $Username@$ServerIP "docker stop \$(docker ps -q --filter ancestor=nginx) 2>/dev/null || echo 'No standalone nginx containers'"
    
    Write-ColorOutput "✅ All nginx instances stopped" $Green
}

function Setup-UnifiedNginx {
    Write-ColorOutput "🌐 Setting up unified nginx configuration..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would setup unified nginx" $Yellow
        return
    }
    
    # Create a unified nginx setup directory
    ssh -i $SSHKeyPath $Username@$ServerIP "mkdir -p /root/nginx-unified"
    
    # Copy the corrected nginx configuration
    ssh -i $SSHKeyPath $Username@$ServerIP "cp /root/mvs/nginx/nginx.conf /root/nginx-unified/nginx.conf"
    
    # Create a unified docker-compose for nginx only
    $unifiedNginxCompose = @"
version: '3.8'

services:
  unified_nginx:
    image: nginx:alpine
    container_name: unified_nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    networks:
      - mvs_production_network
      - mvs_staging_network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  nginx_logs:

networks:
  mvs_production_network:
    external: true
    name: mvs_mvs_production_network
  mvs_staging_network:
    external: true
    name: stagingmvs_mvs_staging_network
"@

    $unifiedNginxCompose | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/nginx-unified/docker-compose.yml"
    
    Write-ColorOutput "✅ Unified nginx configuration created" $Green
}

function Start-UnifiedNginx {
    Write-ColorOutput "🚀 Starting unified nginx..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would start unified nginx" $Yellow
        return
    }
    
    # Start the unified nginx
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/nginx-unified && docker-compose up -d"
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Unified nginx started successfully" $Green
    } else {
        Write-ColorOutput "❌ Failed to start unified nginx" $Red
    }
}

function Remove-NginxFromCompose {
    Write-ColorOutput "🔧 Removing nginx from individual docker-compose files..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would remove nginx from compose files" $Yellow
        return
    }
    
    # Create modified docker-compose files without nginx
    Write-ColorOutput "📝 Creating production compose without nginx..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && cp docker-compose.yml docker-compose.yml.backup"
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && sed '/mvs_nginx:/,/^$/d' docker-compose.yml > docker-compose-no-nginx.yml && mv docker-compose-no-nginx.yml docker-compose.yml"
    
    Write-ColorOutput "📝 Creating staging compose without nginx..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && cp docker-compose.yml docker-compose.yml.backup"
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && sed '/staging_nginx:/,/^$/d' docker-compose.yml > docker-compose-no-nginx.yml && mv docker-compose-no-nginx.yml docker-compose.yml"
    
    Write-ColorOutput "✅ Nginx removed from individual compose files" $Green
}

function Restart-AllServices {
    Write-ColorOutput "🔄 Restarting all services..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would restart all services" $Yellow
        return
    }
    
    # Restart production services (without nginx)
    Write-ColorOutput "🔄 Restarting production services..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/mvs && docker-compose down && docker-compose up -d"
    
    # Restart staging services (without nginx)
    Write-ColorOutput "🔄 Restarting staging services..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose down && docker-compose up -d"
    
    Write-ColorOutput "✅ All services restarted" $Green
}

function Test-UnifiedSetup {
    Write-ColorOutput "🧪 Testing unified setup..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test unified setup" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 30 seconds for services to start..." $Yellow
    Start-Sleep -Seconds 30
    
    # Test both production and staging endpoints
    $testUrls = @(
        @{url="http://mvs.kanousai.com/health"; name="Production Health"},
        @{url="http://mvs.kanousai.com/api/health"; name="Production API"},
        @{url="http://stagingmvs.kanousai.com/health"; name="Staging Health"},
        @{url="http://stagingmvs.kanousai.com/api/health"; name="Staging API"}
    )
    
    foreach ($test in $testUrls) {
        $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' --connect-timeout 5 $($test.url)"
        
        if ($response -eq "200") {
            Write-ColorOutput "✅ $($test.name) - OK (200)" $Green
        } else {
            Write-ColorOutput "❌ $($test.name) - HTTP $response" $Red
        }
    }
    
    # Check nginx status
    Write-ColorOutput "🐳 Checking unified nginx status..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/nginx-unified && docker-compose ps"
}

function Show-FinalStatus {
    Write-ColorOutput "`n📋 Unified Nginx Setup Complete!" $Cyan
    
    Write-ColorOutput "✅ Configuration:" $Green
    Write-ColorOutput "  • Single nginx instance serving both environments" $Cyan
    Write-ColorOutput "  • Production: mvs.kanousai.com → /root/mvs/" $Cyan
    Write-ColorOutput "  • Staging: stagingmvs.kanousai.com → /root/stagingmvs/" $Cyan
    Write-ColorOutput "  • Path-based routing: /api, /admin, /vendor" $Cyan
    
    Write-ColorOutput "`n🌐 Test URLs:" $Cyan
    Write-ColorOutput "  • Production: http://mvs.kanousai.com/" $Cyan
    Write-ColorOutput "  • Staging: http://stagingmvs.kanousai.com/" $Cyan
    
    Write-ColorOutput "`n🎯 DNS Configuration Needed:" $Yellow
    Write-ColorOutput "Add these A records to your domain:" $Yellow
    Write-ColorOutput "mvs.kanousai.com → **************" $Yellow
    Write-ColorOutput "stagingmvs.kanousai.com → **************" $Yellow
}

# Main execution
Write-ColorOutput "🔧 MVS-VR Unified Nginx Setup" $Cyan
Write-ColorOutput "==============================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test SSH connection
Write-ColorOutput "🔍 Testing SSH connection..." $Cyan
ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput "✅ SSH connection successful" $Green
} else {
    Write-ColorOutput "❌ SSH connection failed" $Red
    exit 1
}

# Execute unified nginx setup
Stop-AllNginx
Setup-UnifiedNginx
Remove-NginxFromCompose
Start-UnifiedNginx
Restart-AllServices
Test-UnifiedSetup
Show-FinalStatus

Write-ColorOutput "`n✅ Unified nginx setup completed!" $Green
