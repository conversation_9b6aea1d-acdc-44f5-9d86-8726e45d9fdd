# Update Support Email in Coming Soon Page
# This script updates the coming soon page with the correct support email

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Test-DNS {
    Write-ColorOutput "🌐 Testing DNS resolution..." $Cyan
    
    try {
        $dnsResult = Resolve-DnsName -Name "mvs.kanousai.com" -Type A -ErrorAction SilentlyCatch
        if ($dnsResult -and $dnsResult.IPAddress -contains "**************") {
            Write-ColorOutput "✅ DNS working: mvs.kanousai.com → **************" $Green
            return $true
        } else {
            Write-ColorOutput "⚠️ DNS may still be propagating..." $Yellow
            return $false
        }
    } catch {
        Write-ColorOutput "⚠️ DNS resolution test failed, but may still be working" $Yellow
        return $false
    }
}

function Create-UpdatedComingSoon {
    Write-ColorOutput "📄 Creating updated coming soon page with correct support email..." $Cyan
    
    $updatedNginx = @"
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Upstream definitions for staging services only
    upstream staging_api_gateway {
        server staging_api_gateway:3000;
    }

    upstream staging_directus {
        server staging_directus:8055;
    }

    # PRODUCTION DOMAIN - mvs.kanousai.com (COMING SOON)
    server {
        listen 80;
        server_name mvs.kanousai.com;

        # Coming Soon Page with Contact Form
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Coming Soon</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center;
        }
        .container { 
            max-width: 700px; 
            background: white; 
            padding: 60px 40px; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.3); 
            text-align: center;
        }
        h1 { 
            color: #333; 
            font-size: 3em; 
            margin-bottom: 20px; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle { 
            color: #666; 
            font-size: 1.2em; 
            margin-bottom: 30px; 
        }
        .status { 
            background: #fff3cd; 
            color: #856404; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 30px 0; 
            border-left: 5px solid #ffc107; 
        }
        .features {
            text-align: left;
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            color: #666;
            line-height: 1.8;
        }
        .contact-form {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 5px solid #4caf50;
            text-align: left;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .submit-btn {
            background: #4caf50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        .submit-btn:hover {
            background: #45a049;
        }
        .staging-link {
            display: inline-block;
            padding: 12px 24px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            transition: background 0.3s;
        }
        .staging-link:hover {
            background: #ff5252;
        }
        .info-box {
            background: #e3f2fd;
            color: #1565c0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 MVS-VR</h1>
        <p class='subtitle'>Virtual Reality Content Management Platform</p>
        
        <div class='status'>
            <strong>🚧 Coming Soon</strong><br>
            We're putting the finishing touches on our production environment. 
            The MVS-VR platform will be available soon!
        </div>
        
        <div class='features'>
            <h3>🎯 What's Coming:</h3>
            <ul>
                <li>🏢 <strong>Vendor Portal</strong> - Manage VR content and client experiences</li>
                <li>👥 <strong>Client Access</strong> - Interactive VR experiences</li>
                <li>⚙️ <strong>Admin Dashboard</strong> - System management and analytics</li>
                <li>🔗 <strong>API Gateway</strong> - Seamless integrations</li>
                <li>📊 <strong>Real-time Analytics</strong> - Performance insights</li>
                <li>🤖 <strong>AI-Powered Tools</strong> - Smart content generation</li>
            </ul>
        </div>
        
        <div class='contact-form'>
            <h3>📧 Get Notified When We Launch</h3>
            <form action='mailto:<EMAIL>' method='post' enctype='text/plain'>
                <div class='form-group'>
                    <label for='name'>Name:</label>
                    <input type='text' id='name' name='name' required>
                </div>
                <div class='form-group'>
                    <label for='email'>Email:</label>
                    <input type='email' id='email' name='email' required>
                </div>
                <div class='form-group'>
                    <label for='company'>Company (Optional):</label>
                    <input type='text' id='company' name='company'>
                </div>
                <div class='form-group'>
                    <label for='interest'>Interest:</label>
                    <textarea id='interest' name='interest' placeholder='Tell us about your VR content needs...'></textarea>
                </div>
                <button type='submit' class='submit-btn'>📬 Notify Me</button>
            </form>
        </div>
        
        <div class='info-box'>
            <strong>🌟 Early Access:</strong> Sign up above to get priority access and special launch pricing!
        </div>
        
        <a href='http://stagingmvs.kanousai.com' class='staging-link'>
            🧪 View Development Preview
        </a>
        
        <p style='color: #999; font-size: 0.9em; margin-top: 30px;'>
            Server Status: Online | Environment: Production Setup | Version: 2.0<br>
            Domain: mvs.kanousai.com | Server: **************
        </p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }

        # Health check still available
        location /health {
            access_log off;
            return 200 "MVS-VR Production - Coming Soon
Status: Preparing for launch
Domain: mvs.kanousai.com
Server: **************
Staging: http://stagingmvs.kanousai.com
";
            add_header Content-Type text/plain;
        }
    }

    # STAGING DOMAIN - stagingmvs.kanousai.com (ACTIVE)
    server {
        listen 80;
        server_name stagingmvs.kanousai.com;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Staging - Active
Environment: Staging
Domain: stagingmvs.kanousai.com
Server Path: /root/stagingmvs/
API: stagingmvs.kanousai.com/api
Admin: stagingmvs.kanousai.com/admin
Vendor: stagingmvs.kanousai.com/vendor
";
            add_header Content-Type text/plain;
        }

        # API Gateway - Staging
        location /api/ {
            proxy_pass http://staging_api_gateway/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Admin Panel - Staging
        location /admin/ {
            proxy_pass http://staging_directus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Vendor Portal - Staging
        location /vendor/ {
            proxy_pass http://staging_api_gateway/vendor/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Home Page - Staging
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Staging Environment</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 10px; }
        .subtitle { text-align: center; color: #666; margin-bottom: 30px; }
        .warning { background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #ffc107; }
        .status { background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #4caf50; }
        .login-options { display: flex; gap: 20px; margin: 30px 0; }
        .login-card { flex: 1; background: #f8f9fa; padding: 25px; border-radius: 10px; text-align: center; border: 2px solid #e9ecef; }
        .btn { display: inline-block; padding: 12px 24px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 5px; }
        .links { text-align: center; margin: 30px 0; }
        .links a { color: #ff6b6b; text-decoration: none; margin: 0 15px; }
        .environment { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; text-align: center; font-weight: bold; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 MVS-VR Staging</h1>
        <p class='subtitle'>Development Preview Environment</p>
        
        <div class='environment'>⚠️ STAGING ENVIRONMENT - FOR TESTING ONLY</div>
        
        <div class='warning'>
            <strong>⚠️ Development Preview:</strong> This is a staging environment for testing and development. Features may be incomplete and data may be reset without notice.
        </div>
        
        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🌐 Domain:</strong> stagingmvs.kanousai.com<br>
            <strong>📡 Server:</strong> ************** (/root/stagingmvs/)<br>
            <strong>🔗 Database:</strong> Staging Database Connected<br>
            <strong>🚀 Production:</strong> <a href='http://mvs.kanousai.com'>Coming Soon</a>
        </div>
        
        <div class='login-options'>
            <div class='login-card'>
                <h3>🏢 Vendor Preview</h3>
                <p>Preview vendor functionality and test new features before production deployment.</p>
                <a href='/vendor/' class='btn'>Preview Vendor Portal</a>
            </div>
            <div class='login-card'>
                <h3>👥 Client Preview</h3>
                <p>Preview client experiences and test functionality.</p>
                <a href='/api/client-portal' class='btn'>Preview Client Portal</a>
            </div>
        </div>
        
        <div class='links'>
            <a href='/api/health'>API Status</a> |
            <a href='/admin/'>Admin Preview</a> |
            <a href='http://mvs.kanousai.com'>Production Site</a>
        </div>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # DEFAULT/FALLBACK - Handle IP access and unknown domains
    server {
        listen 80 default_server;
        server_name _;

        # Redirect IP access to production for now
        location / {
            return 301 http://mvs.kanousai.com$request_uri;
        }

        # Health check for IP access
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure
Production: mvs.kanousai.com (Coming Soon)
Staging: stagingmvs.kanousai.com (Active)
Server: **************
";
            add_header Content-Type text/plain;
        }
    }
}
"@

    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would create updated nginx configuration" $Yellow
    } else {
        # Create updated nginx config
        $updatedNginx | ssh -i $SSHKeyPath $Username@$ServerIP "cat > /root/nginx-updated.conf"
        Write-ColorOutput "✅ Updated nginx configuration created" $Green
    }
}

function Update-NginxContainer {
    Write-ColorOutput "🔄 Updating nginx container with new configuration..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would update nginx container" $Yellow
        return
    }
    
    # Stop current nginx
    ssh -i $SSHKeyPath $Username@$ServerIP "docker stop coming-soon-nginx && docker rm coming-soon-nginx"
    
    # Start new nginx with updated config
    ssh -i $SSHKeyPath $Username@$ServerIP "docker run -d --name coming-soon-nginx --restart always -p 80:80 -v /root/nginx-updated.conf:/etc/nginx/nginx.conf:ro --network stagingmvs_mvs_staging_network nginx:alpine"
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Nginx container updated successfully" $Green
    } else {
        Write-ColorOutput "❌ Failed to update nginx container" $Red
    }
}

function Test-UpdatedSite {
    Write-ColorOutput "🧪 Testing updated site..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test updated site" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 10 seconds for nginx to start..." $Yellow
    Start-Sleep -Seconds 10
    
    # Test from server
    Write-ColorOutput "Testing from server..." $Cyan
    $serverTest = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' http://localhost/"
    
    if ($serverTest -eq "200") {
        Write-ColorOutput "✅ Server test: Coming soon page working (200)" $Green
    } else {
        Write-ColorOutput "❌ Server test failed: HTTP $serverTest" $Red
    }
    
    # Test health endpoint
    $healthTest = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' http://localhost/health"
    
    if ($healthTest -eq "200") {
        Write-ColorOutput "✅ Health endpoint working (200)" $Green
    } else {
        Write-ColorOutput "❌ Health endpoint failed: HTTP $healthTest" $Red
    }
}

function Show-Summary {
    Write-ColorOutput "`n📋 Support Email Update Complete!" $Cyan
    
    Write-ColorOutput "✅ Updates Made:" $Green
    Write-ColorOutput "  • Support email updated to: <EMAIL> (private)" $Cyan
    Write-ColorOutput "  • Contact form added to coming soon page" $Cyan
    Write-ColorOutput "  • Enhanced staging environment description" $Cyan
    Write-ColorOutput "  • Improved visual design and user experience" $Cyan
    
    Write-ColorOutput "`n🌐 Current Status:" $Cyan
    Write-ColorOutput "  • mvs.kanousai.com → Enhanced coming soon page with contact form" $Cyan
    Write-ColorOutput "  • stagingmvs.kanousai.com → Active staging environment" $Cyan
    Write-ColorOutput "  • Contact forms → <EMAIL> (not publicly displayed)" $Cyan
    
    Write-ColorOutput "`n🎯 Features Added:" $Yellow
    Write-ColorOutput "  • Contact form for early access notifications" $Yellow
    Write-ColorOutput "  • Professional design with company branding" $Yellow
    Write-ColorOutput "  • Clear staging vs production differentiation" $Yellow
    Write-ColorOutput "  • Email protection (support email not publicly visible)" $Yellow
}

# Main execution
Write-ColorOutput "📧 MVS-VR Support Email Update" $Cyan
Write-ColorOutput "==============================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test DNS
Test-DNS

# Test SSH connection
Write-ColorOutput "🔍 Testing SSH connection..." $Cyan
ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput "✅ SSH connection successful" $Green
} else {
    Write-ColorOutput "❌ SSH connection failed" $Red
    exit 1
}

# Execute updates
Create-UpdatedComingSoon
Update-NginxContainer
Test-UpdatedSite
Show-Summary

Write-ColorOutput "`n✅ Support email update completed!" $Green
