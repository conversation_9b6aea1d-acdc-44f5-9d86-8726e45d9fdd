# 🏗️ MVS-VR Corrected Server Structure Implementation Plan

## 🎯 **Gap Analysis Summary**

### **Current Issues Identified:**
1. ❌ **Wrong Directory Structure**: Using `/home/<USER>/mvs-vr-deployment` instead of `/root/mvs` and `/root/stagingmvs`
2. ❌ **Incorrect Domain Routing**: Using subdomains instead of path-based routing (`/admin`, `/vendor`)
3. ❌ **Missing Root Domain**: No `kanousai.com` setup
4. ❌ **Complex DNS**: Unnecessary subdomains when paths would be simpler
5. ❌ **Missing Login Differentiation**: No vendor vs client login options

### **Required Structure:**
```
kanousai.com (Root - separate setup)
├── mvs.kanousai.com → /root/mvs/ (Production)
│   ├── / (Home: vendor/client login options)
│   ├── /api (API Gateway)
│   ├── /admin (System admin panel)
│   └── /vendor (Vendor portal)
└── stagingmvs.kanousai.com → /root/stagingmvs/ (Staging)
    ├── / (Staging: vendor/client login options)
    ├── /api (Staging API)
    ├── /admin (Staging admin)
    └── /vendor (Staging vendor portal)
```

## 🚀 **Implementation Plan**

### **Phase 1: Server Directory Restructure**

#### **Step 1.1: Create New Directory Structure**
```bash
# SSH into server
ssh -i C:\Users\<USER>\.ssh\mvs-vr root@**************

# Create new directory structure
mkdir -p /root/mvs
mkdir -p /root/stagingmvs

# Move existing deployment (if needed)
if [ -d "/home/<USER>/mvs-vr-deployment" ]; then
    cp -r /home/<USER>/mvs-vr-deployment/* /root/mvs/
fi
```

#### **Step 1.2: Update Docker Compose for New Paths**
- Production: `/root/mvs/docker-compose.yml`
- Staging: `/root/stagingmvs/docker-compose.yml`

#### **Step 1.3: Create Unified Nginx Configuration**
- Single nginx config handling both domains
- Path-based routing instead of subdomains
- Vendor/client login differentiation

### **Phase 2: Domain Configuration Update**

#### **Step 2.1: Simplified DNS Requirements**
```
# DNS Records Needed:
kanousai.com              A    **************  (Root domain)
mvs.kanousai.com          A    **************  (Production)
stagingmvs.kanousai.com   A    **************  (Staging)
```

#### **Step 2.2: Path-Based Routing**
- `/api` → API Gateway
- `/admin` → System Admin Panel (Directus)
- `/vendor` → Vendor Portal
- `/` → Home page with login options

### **Phase 3: Implementation Files**

#### **Step 3.1: Updated Nginx Configuration**
- Remove subdomain complexity
- Add path-based routing
- Include vendor/client login page

#### **Step 3.2: Updated Docker Compose**
- Correct volume paths
- Proper service networking
- Environment-specific configurations

#### **Step 3.3: Deployment Scripts**
- Automated deployment to correct paths
- Environment detection
- Health checks

## 📝 **Detailed Implementation**

### **1. Corrected Nginx Configuration**

The nginx configuration will be updated to handle:
- `mvs.kanousai.com` → Production environment at `/root/mvs/`
- `stagingmvs.kanousai.com` → Staging environment at `/root/stagingmvs/`
- Path-based routing for `/api`, `/admin`, `/vendor`
- Unified home page with vendor/client login options

### **2. Directory Structure on Server**

```
/root/
├── mvs/ (Production)
│   ├── docker-compose.yml
│   ├── nginx/
│   │   └── nginx.conf
│   ├── services/
│   └── data/
└── stagingmvs/ (Staging)
    ├── docker-compose.yml
    ├── nginx/
    │   └── nginx.conf
    ├── services/
    └── data/
```

### **3. DNS Configuration for Domain Host**

**Required DNS Records:**
```
Type: A    Name: @              Value: **************  (kanousai.com root)
Type: A    Name: mvs            Value: **************  (mvs.kanousai.com)
Type: A    Name: stagingmvs     Value: **************  (stagingmvs.kanousai.com)
```

**No longer needed:**
- `api.mvs.kanousai.com` (will be `/api` path)
- `admin.mvs.kanousai.com` (will be `/admin` path)

## ✅ **Next Steps**

1. **Review and approve this plan**
2. **Create corrected nginx configuration**
3. **Create deployment scripts for new structure**
4. **Provide DNS instructions for domain host**
5. **Execute migration to new structure**

This plan simplifies the architecture while meeting your requirements for:
- Root domain separation (`kanousai.com`)
- Production at `/root/mvs/`
- Staging at `/root/stagingmvs/`
- Path-based routing instead of subdomains
- Vendor/client login differentiation

## 📁 **Implementation Files Created**

### **1. nginx-corrected.conf**
- Unified nginx configuration for both environments
- Path-based routing (`/api`, `/admin`, `/vendor`)
- Professional home pages with vendor/client login options
- Proper upstream definitions for production and staging

### **2. docker-compose-production.yml**
- Production environment at `/root/mvs/`
- All services with `mvs_` prefix
- Production-specific environment variables
- Proper volume and network isolation

### **3. docker-compose-staging.yml**
- Staging environment at `/root/stagingmvs/`
- All services with `staging_` prefix
- Staging-specific environment variables
- Separate volumes and networks

### **4. Deploy-Corrected-Structure.ps1**
- Automated deployment script
- Creates correct directory structure
- Uploads configurations and Docker images
- Starts services and runs health checks
- Supports dry-run mode for testing

### **5. DNS_CONFIGURATION_CORRECTED.md**
- Simplified DNS requirements (only 3 A records)
- Clear instructions for domain host
- Verification commands and testing procedures
- Expected results and troubleshooting

## 🚀 **Deployment Instructions**

### **Step 1: Review and Approve**
1. Review all created files in `docker-exports/`
2. Verify the structure meets your requirements
3. Approve the implementation plan

### **Step 2: Execute Deployment**
```powershell
# Navigate to docker-exports directory
cd C:\Users\<USER>\projects\MVS-VR\mvs-vr-v2\docker-exports

# Run deployment script (dry-run first)
.\Deploy-Corrected-Structure.ps1 -DryRun

# If dry-run looks good, execute actual deployment
.\Deploy-Corrected-Structure.ps1
```

### **Step 3: Configure DNS**
1. Contact your domain registrar/DNS provider
2. Add the 3 A records specified in `DNS_CONFIGURATION_CORRECTED.md`
3. Wait for DNS propagation (5-60 minutes)

### **Step 4: Update Environment Variables**
1. SSH into server: `ssh -i C:\Users\<USER>\.ssh\mvs-vr root@**************`
2. Edit `/root/mvs/.env` with production Supabase credentials
3. Edit `/root/stagingmvs/.env` with staging credentials
4. Restart services: `cd /root/mvs && docker-compose restart`

### **Step 5: Test Everything**
```bash
# Test production
curl -I http://mvs.kanousai.com/
curl -I http://mvs.kanousai.com/api/health
curl -I http://mvs.kanousai.com/admin/
curl -I http://mvs.kanousai.com/vendor/

# Test staging
curl -I http://stagingmvs.kanousai.com/
curl -I http://stagingmvs.kanousai.com/api/health
```

## ✅ **Benefits of This Structure**

1. **Simplified DNS**: Only 3 A records instead of complex subdomains
2. **Clear Separation**: Production and staging completely isolated
3. **Path-Based Routing**: Easier to manage and understand
4. **Correct Directory Structure**: Matches your requirements exactly
5. **Vendor/Client Differentiation**: Clear login options on home page
6. **Future-Proof**: Easy to add `kanousai.com` root domain later
7. **Maintainable**: Single nginx config handles both environments
8. **Scalable**: Easy to add more environments or services

## 🎯 **Final Structure Summary**

```
DNS:
├── kanousai.com → ************** (future root domain)
├── mvs.kanousai.com → ************** (production)
└── stagingmvs.kanousai.com → ************** (staging)

Server:
├── /root/mvs/ (production)
│   ├── docker-compose.yml
│   ├── .env
│   └── nginx/nginx.conf
└── /root/stagingmvs/ (staging)
    ├── docker-compose.yml
    ├── .env
    └── nginx/nginx.conf

URLs:
├── mvs.kanousai.com/ (vendor/client login)
├── mvs.kanousai.com/api (API gateway)
├── mvs.kanousai.com/admin (system admin)
├── mvs.kanousai.com/vendor (vendor portal)
└── stagingmvs.kanousai.com/* (staging versions)
```

This implementation fully addresses your requirements and provides a clean, maintainable architecture.
