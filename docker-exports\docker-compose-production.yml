# MVS-VR Production Environment - /root/mvs/
version: '3.8'

services:
  # Production API Gateway
  mvs_api_gateway:
    image: mvs-vr/api-gateway:latest
    container_name: mvs_api_gateway
    restart: always
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
      - DIRECTUS_URL=http://mvs_directus:8055
      - DIRECTUS_SECRET=${DIRECTUS_SECRET:-directus-secret}
      - ENVIRONMENT=production
      - DOMAIN=mvs.kanousai.com
    volumes:
      - mvs_api_data:/app/data
      - mvs_api_logs:/app/logs
      - mvs_api_uploads:/app/uploads
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis
      - mvs_directus
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Production Directus CMS
  mvs_directus:
    image: directus/directus:latest
    container_name: mvs_directus
    restart: always
    environment:
      - KEY=${DIRECTUS_KEY:-directus-key}
      - SECRET=${DIRECTUS_SECRET:-directus-secret}
      - DB_CLIENT=pg
      - DB_HOST=${SUPABASE_DB_HOST}
      - DB_PORT=${SUPABASE_DB_PORT:-5432}
      - DB_DATABASE=${SUPABASE_DB_NAME}
      - DB_USER=${SUPABASE_DB_USER}
      - DB_PASSWORD=${SUPABASE_DB_PASSWORD}
      - CACHE_ENABLED=true
      - CACHE_STORE=redis
      - CACHE_REDIS=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
      - ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
      - PUBLIC_URL=http://mvs.kanousai.com/admin
      - CORS_ENABLED=true
      - CORS_ORIGIN=http://mvs.kanousai.com
    volumes:
      - mvs_directus_uploads:/directus/uploads
      - mvs_directus_extensions:/directus/extensions
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8055/server/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Production Redis Cache
  mvs_redis:
    image: redis:7-alpine
    container_name: mvs_redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - mvs_redis_data:/data
    networks:
      - mvs_production_network
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD:-redis_password}', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # Production Auth Service
  mvs_auth_service:
    image: mvs-vr/auth-service:latest
    container_name: mvs_auth_service
    restart: always
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
      - JWT_SECRET=${JWT_SECRET:-jwt-secret}
    volumes:
      - mvs_auth_data:/app/data
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis

  # Production Asset Service
  mvs_asset_service:
    image: mvs-vr/asset-service:latest
    container_name: mvs_asset_service
    restart: always
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
      - STORAGE_PATH=/app/uploads
    volumes:
      - mvs_asset_uploads:/app/uploads
      - mvs_asset_data:/app/data
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis

  # Production Blueprint Service
  mvs_blueprint_service:
    image: mvs-vr/blueprint-service:latest
    container_name: mvs_blueprint_service
    restart: always
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
    volumes:
      - mvs_blueprint_data:/app/data
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis

  # Production Analytics Service
  mvs_analytics_service:
    image: mvs-vr/analytics-service:latest
    container_name: mvs_analytics_service
    restart: always
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
    volumes:
      - mvs_analytics_data:/app/data
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis

  # Production LLM Service
  mvs_llm_service:
    image: mvs-vr/llm-service:latest
    container_name: mvs_llm_service
    restart: always
    environment:
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
    volumes:
      - mvs_llm_data:/app/data
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis

  # Production Monitoring Service
  mvs_monitoring_service:
    image: mvs-vr/monitoring-service:latest
    container_name: mvs_monitoring_service
    restart: always
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-redis_password}@mvs_redis:6379
    volumes:
      - mvs_monitoring_data:/app/data
      - mvs_monitoring_metrics:/app/metrics
    networks:
      - mvs_production_network
    depends_on:
      - mvs_redis

  # Production Nginx Reverse Proxy
  mvs_nginx:
    image: nginx:alpine
    container_name: mvs_nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-corrected.conf:/etc/nginx/nginx.conf:ro
      - mvs_nginx_logs:/var/log/nginx
    networks:
      - mvs_production_network
    depends_on:
      - mvs_api_gateway
      - mvs_directus
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mvs_api_data:
  mvs_api_logs:
  mvs_api_uploads:
  mvs_directus_uploads:
  mvs_directus_extensions:
  mvs_redis_data:
  mvs_auth_data:
  mvs_asset_uploads:
  mvs_asset_data:
  mvs_blueprint_data:
  mvs_analytics_data:
  mvs_llm_data:
  mvs_monitoring_data:
  mvs_monitoring_metrics:
  mvs_nginx_logs:

networks:
  mvs_production_network:
    driver: bridge
