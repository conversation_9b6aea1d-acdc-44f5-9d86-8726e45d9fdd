# 🌐 DNS Configuration Instructions - CRITICAL NEXT STEP

## 🚨 **Current Issue Identified**

The domains are currently pointing to **Cloudflare proxy servers** instead of your server `**************`. This is why you're getting 521 errors.

## ✅ **Current Status**
- **✅ Production Services**: Stopped (as requested)
- **✅ Coming Soon Page**: Deployed and ready
- **✅ Staging Environment**: Active and working
- **⚠️ DNS Issue**: Domains going through Cloudflare instead of direct to server

## 🎯 **How Domain Differentiation Works**

You asked about domain differentiation - here's how it works:

### **Same IP, Different Domains → Different Folders**
```
All domains point to: **************
↓
Ngin<PERSON> receives request and checks "Host" header:
├── Host: mvs.kanousai.com → Routes to production services (/root/mvs/)
├── Host: stagingmvs.kanousai.com → Routes to staging services (/root/stagingmvs/)
└── Host: kanousai.com → Routes to root domain setup (future)
```

This is standard web server behavior - **one IP can serve multiple domains**.

## 🔧 **DNS Configuration Required**

### **Option 1: Direct DNS (Recommended)**
Configure your domain registrar to point directly to your server:

```
Type: A    Name: mvs           Value: **************    TTL: 300
Type: A    Name: stagingmvs    Value: **************    TTL: 300
Type: A    Name: @             Value: **************    TTL: 300
```

### **Option 2: Cloudflare Configuration**
If you want to keep Cloudflare, configure it properly:

1. **In Cloudflare Dashboard:**
   - Set `mvs.kanousai.com` → `**************` (DNS Only - Gray Cloud)
   - Set `stagingmvs.kanousai.com` → `**************` (DNS Only - Gray Cloud)
   - Set `kanousai.com` → `**************` (DNS Only - Gray Cloud)

2. **Turn OFF Cloudflare Proxy** (Gray cloud, not orange)

## 📋 **Step-by-Step DNS Configuration**

### **Step 1: Access Your DNS Provider**
- Log into your domain registrar (where you bought kanousai.com)
- OR log into Cloudflare if you're using their DNS

### **Step 2: Add/Update A Records**
```
Record 1:
Type: A
Name: mvs
Value: **************
TTL: 300 (5 minutes)

Record 2:
Type: A  
Name: stagingmvs
Value: **************
TTL: 300

Record 3:
Type: A
Name: @ (or blank for root)
Value: **************
TTL: 300
```

### **Step 3: If Using Cloudflare**
- **IMPORTANT**: Set proxy status to "DNS Only" (gray cloud ☁️)
- **NOT** "Proxied" (orange cloud 🟠)

### **Step 4: Verify DNS Propagation**
```bash
# Check DNS resolution (run these commands):
nslookup mvs.kanousai.com
nslookup stagingmvs.kanousai.com
nslookup kanousai.com

# Should all return: **************
```

## 🧪 **Testing After DNS Configuration**

Once DNS is configured, test these URLs:

### **Production (Coming Soon Page):**
- `http://mvs.kanousai.com/` → Should show coming soon page
- `http://mvs.kanousai.com/health` → Should return health status

### **Staging (Active Environment):**
- `http://stagingmvs.kanousai.com/` → Should show staging home
- `http://stagingmvs.kanousai.com/health` → Should return staging health
- `http://stagingmvs.kanousai.com/api/health` → Should return API health

## ⚙️ **Update Staging Environment Variables**

After DNS is working, update staging with real credentials:

```bash
# SSH into server
ssh -i C:\Users\<USER>\mvs-vr root@**************

# Edit staging environment
nano /root/stagingmvs/.env

# Update these values:
STAGING_SUPABASE_URL=https://your-real-staging-project.supabase.co
STAGING_SUPABASE_ANON_KEY=your_real_staging_anon_key
STAGING_SUPABASE_SERVICE_ROLE_KEY=your_real_staging_service_role_key
STAGING_SUPABASE_DB_HOST=db.your-real-staging-project.supabase.co
STAGING_SUPABASE_DB_PASSWORD=your_real_staging_db_password

# Restart staging services
cd /root/stagingmvs
docker-compose restart
```

## 🎯 **Alternative: Single Domain Structure**

If you prefer to avoid multiple domains, we can restructure to use only `mvs.kanousai.com`:

```
mvs.kanousai.com/
├── / → Production home
├── /staging/ → Staging environment  
├── /api/ → Production API
├── /staging/api/ → Staging API
├── /admin/ → Production admin
├── /staging/admin/ → Staging admin
└── /vendor/ → Production vendor
```

**Would you prefer this single-domain approach?**

## 📞 **Contact Your Domain Provider**

**Call/email your domain registrar and say:**

> "I need to update the DNS A records for kanousai.com. Please set:
> 
> - mvs.kanousai.com to point to **************
> - stagingmvs.kanousai.com to point to **************  
> - kanousai.com root domain to point to **************
> 
> TTL should be 300 seconds. If you're using Cloudflare, please set these to 'DNS Only' not 'Proxied'."

## ⏰ **Timeline**

- **DNS Update**: 5-15 minutes
- **Global Propagation**: 30 minutes - 2 hours
- **Full Propagation**: Up to 24 hours

## ✅ **Next Steps Summary**

1. **🔴 CRITICAL**: Configure DNS A records (see above)
2. **🟡 IMPORTANT**: Update staging environment variables
3. **🟢 OPTIONAL**: Test all endpoints after DNS propagation
4. **🔵 FUTURE**: Prepare production environment when ready

## 🎉 **After DNS Configuration**

Once DNS is working, you'll have:

- **mvs.kanousai.com** → Professional coming soon page
- **stagingmvs.kanousai.com** → Fully functional staging environment
- **kanousai.com** → Ready for root domain setup

The infrastructure is ready - DNS configuration is the only blocker!
