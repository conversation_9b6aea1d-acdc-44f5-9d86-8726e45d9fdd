# Fix Connection Refused Issue
# This script diagnoses and fixes the connection refused error

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Test-ServerConnectivity {
    Write-ColorOutput "🔍 Testing server connectivity..." $Cyan
    
    # Test ping
    Write-ColorOutput "Testing ping to $ServerIP..." $Cyan
    $pingResult = Test-Connection -ComputerName $ServerIP -Count 2 -Quiet
    
    if ($pingResult) {
        Write-ColorOutput "✅ Server is reachable via ping" $Green
    } else {
        Write-ColorOutput "❌ Server is not reachable via ping" $Red
        return $false
    }
    
    # Test port 80
    Write-ColorOutput "Testing port 80..." $Cyan
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync($ServerIP, 80).Wait(5000)
        if ($tcpClient.Connected) {
            Write-ColorOutput "✅ Port 80 is open" $Green
            $tcpClient.Close()
        } else {
            Write-ColorOutput "❌ Port 80 is not responding" $Red
            return $false
        }
    } catch {
        Write-ColorOutput "❌ Cannot connect to port 80: $($_.Exception.Message)" $Red
        return $false
    }
    
    return $true
}

function Test-SSH {
    Write-ColorOutput "🔍 Testing SSH connection..." $Cyan
    
    try {
        $sshTest = ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH OK'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ SSH connection working" $Green
            return $true
        } else {
            Write-ColorOutput "❌ SSH connection failed" $Red
            return $false
        }
    } catch {
        Write-ColorOutput "❌ SSH test failed: $($_.Exception.Message)" $Red
        return $false
    }
}

function Check-DockerStatus {
    Write-ColorOutput "🐳 Checking Docker containers..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would check Docker status" $Yellow
        return
    }
    
    try {
        $dockerPs = ssh -i $SSHKeyPath $Username@$ServerIP "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "Docker containers status:" $Cyan
            Write-ColorOutput $dockerPs $White
        } else {
            Write-ColorOutput "❌ Failed to get Docker status" $Red
        }
    } catch {
        Write-ColorOutput "❌ Docker check failed: $($_.Exception.Message)" $Red
    }
}

function Restart-NginxService {
    Write-ColorOutput "🔄 Restarting nginx service..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would restart nginx service" $Yellow
        return
    }
    
    try {
        # Stop any existing nginx containers
        Write-ColorOutput "Stopping existing nginx containers..." $Cyan
        ssh -i $SSHKeyPath $Username@$ServerIP "docker stop \$(docker ps -q --filter ancestor=nginx) 2>/dev/null || echo 'No nginx containers running'"
        
        # Remove stopped containers
        ssh -i $SSHKeyPath $Username@$ServerIP "docker rm \$(docker ps -aq --filter ancestor=nginx) 2>/dev/null || echo 'No nginx containers to remove'"
        
        # Start new nginx with coming soon page
        Write-ColorOutput "Starting new nginx container..." $Cyan
        $startResult = ssh -i $SSHKeyPath $Username@$ServerIP "docker run -d --name mvs-nginx --restart always -p 80:80 -v /root/nginx-coming-soon.conf:/etc/nginx/nginx.conf:ro --network stagingmvs_mvs_staging_network nginx:alpine 2>&1"
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Nginx container started successfully" $Green
            Write-ColorOutput "Container ID: $startResult" $Cyan
        } else {
            Write-ColorOutput "❌ Failed to start nginx container: $startResult" $Red
            
            # Try alternative approach - standalone nginx
            Write-ColorOutput "Trying standalone nginx..." $Yellow
            $standaloneResult = ssh -i $SSHKeyPath $Username@$ServerIP "docker run -d --name mvs-nginx-standalone --restart always -p 80:80 nginx:alpine 2>&1"
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Standalone nginx started" $Green
                
                # Copy basic config
                ssh -i $SSHKeyPath $Username@$ServerIP "docker exec mvs-nginx-standalone sh -c 'echo \"server { listen 80; location / { return 200 \\\"MVS-VR Coming Soon - Server Online\\\"; add_header Content-Type text/plain; } }\" > /etc/nginx/conf.d/default.conf && nginx -s reload'"
            } else {
                Write-ColorOutput "❌ Standalone nginx also failed: $standaloneResult" $Red
            }
        }
    } catch {
        Write-ColorOutput "❌ Nginx restart failed: $($_.Exception.Message)" $Red
    }
}

function Test-WebService {
    Write-ColorOutput "🧪 Testing web service..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test web service" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 10 seconds for service to start..." $Yellow
    Start-Sleep -Seconds 10
    
    # Test from server localhost
    try {
        $localhostTest = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'FAILED'"
        
        if ($localhostTest -eq "200") {
            Write-ColorOutput "✅ Localhost test: HTTP 200 OK" $Green
        } else {
            Write-ColorOutput "❌ Localhost test failed: $localhostTest" $Red
        }
    } catch {
        Write-ColorOutput "❌ Localhost test error: $($_.Exception.Message)" $Red
    }
    
    # Test external access
    try {
        Write-ColorOutput "Testing external access..." $Cyan
        $externalTest = Invoke-WebRequest -Uri "http://$ServerIP" -TimeoutSec 10 -UseBasicParsing -ErrorAction SilentlyContinue
        
        if ($externalTest.StatusCode -eq 200) {
            Write-ColorOutput "✅ External access: HTTP 200 OK" $Green
        } else {
            Write-ColorOutput "❌ External access failed: HTTP $($externalTest.StatusCode)" $Red
        }
    } catch {
        Write-ColorOutput "❌ External access error: $($_.Exception.Message)" $Red
    }
}

function Show-DiagnosticInfo {
    Write-ColorOutput "`n📋 Diagnostic Information:" $Cyan
    
    Write-ColorOutput "🌐 Domain Information:" $Cyan
    Write-ColorOutput "  • Target Domain: mvs.kanousai.com" $White
    Write-ColorOutput "  • Server IP: $ServerIP" $White
    Write-ColorOutput "  • Expected Port: 80 (HTTP)" $White
    
    Write-ColorOutput "`n🔧 Troubleshooting Steps:" $Yellow
    Write-ColorOutput "1. Check if server is online" $White
    Write-ColorOutput "2. Verify SSH connectivity" $White
    Write-ColorOutput "3. Check Docker containers status" $White
    Write-ColorOutput "4. Restart nginx service" $White
    Write-ColorOutput "5. Test web service accessibility" $White
    
    Write-ColorOutput "`n📞 If Issues Persist:" $Yellow
    Write-ColorOutput "• Check server firewall settings" $White
    Write-ColorOutput "• Verify DNS propagation" $White
    Write-ColorOutput "• Check server resource usage" $White
    Write-ColorOutput "• Review Docker logs" $White
}

# Main execution
Write-ColorOutput "🔧 MVS-VR Connection Refused Fix" $Cyan
Write-ColorOutput "=================================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

Show-DiagnosticInfo

# Run diagnostic steps
$serverReachable = Test-ServerConnectivity
$sshWorking = Test-SSH

if ($serverReachable -and $sshWorking) {
    Write-ColorOutput "`n✅ Server connectivity confirmed - proceeding with fixes..." $Green
    Check-DockerStatus
    Restart-NginxService
    Test-WebService
} elseif ($serverReachable) {
    Write-ColorOutput "`n⚠️ Server reachable but SSH issues - manual intervention may be needed" $Yellow
} else {
    Write-ColorOutput "`n❌ Server connectivity issues - check server status" $Red
}

Write-ColorOutput "`n📋 Next Steps:" $Cyan
if ($serverReachable -and $sshWorking) {
    Write-ColorOutput "• Test mvs.kanousai.com in browser" $Yellow
    Write-ColorOutput "• If still failing, check DNS propagation" $Yellow
    Write-ColorOutput "• Monitor Docker container logs" $Yellow
} else {
    Write-ColorOutput "• Check server status and connectivity" $Yellow
    Write-ColorOutput "• Verify SSH key and permissions" $Yellow
    Write-ColorOutput "• Contact server provider if needed" $Yellow
}

Write-ColorOutput "`n✅ Diagnostic completed!" $Green
