# Fix Docker Images Script
# This script fixes the Docker images that were uploaded but failed to load

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$SSHKeyPath = "C:\Users\<USER>\mvs-vr",
    [switch]$DryRun = $false
)

# Define colors for output
$Green = [ConsoleColor]::Green
$Yellow = [ConsoleColor]::Yellow
$Red = [ConsoleColor]::Red
$Cyan = [ConsoleColor]::Cyan

function Write-ColorOutput {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    Write-Host $Message -ForegroundColor $Color
}

function Fix-DockerImages {
    Write-ColorOutput "🔧 Fixing Docker images on server..." $Cyan
    
    # List of images that need to be fixed
    $imageNames = @(
        "analytics-service-20250601-192616",
        "api-gateway-20250601-192616", 
        "asset-service-20250601-192616",
        "auth-service-20250601-192616",
        "blueprint-service-20250601-192616",
        "directus-20250601-192616",
        "llm-service-20250601-192616",
        "monitoring-service-20250601-192616"
    )
    
    foreach ($imageName in $imageNames) {
        Write-ColorOutput "🔄 Processing $imageName..." $Cyan
        
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would fix Docker image: $imageName" $Yellow
        } else {
            # Check if the zip file exists
            $zipExists = ssh -i $SSHKeyPath $Username@$ServerIP "test -f /mnt/volume_blr1_01/mvs-vr-project/docker-images/$imageName.tar.zip && echo 'exists'"
            
            if ($zipExists -eq "exists") {
                Write-ColorOutput "📦 Found $imageName.tar.zip, extracting and loading..." $Cyan
                
                # Extract and load the image properly
                $loadResult = ssh -i $SSHKeyPath $Username@$ServerIP "cd /mnt/volume_blr1_01/mvs-vr-project/docker-images && unzip -o $imageName.tar.zip && docker load < $imageName.tar && rm $imageName.tar"
                
                if ($LASTEXITCODE -eq 0) {
                    Write-ColorOutput "✅ Successfully loaded Docker image: $imageName" $Green
                } else {
                    Write-ColorOutput "❌ Failed to load Docker image: $imageName" $Red
                }
            } else {
                Write-ColorOutput "⚠️ Zip file not found: $imageName.tar.zip" $Yellow
            }
        }
    }
}

function List-DockerImages {
    Write-ColorOutput "🐳 Listing available Docker images..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would list Docker images" $Yellow
    } else {
        ssh -i $SSHKeyPath $Username@$ServerIP "docker images"
    }
}

function Tag-DockerImages {
    Write-ColorOutput "🏷️ Tagging Docker images for staging..." $Cyan
    
    # Map of image files to proper tags
    $imageTags = @{
        "analytics-service-20250601-192616" = "mvs-vr/analytics-service:latest"
        "api-gateway-20250601-192616" = "mvs-vr/api-gateway:latest"
        "asset-service-20250601-192616" = "mvs-vr/asset-service:latest"
        "auth-service-20250601-192616" = "mvs-vr/auth-service:latest"
        "blueprint-service-20250601-192616" = "mvs-vr/blueprint-service:latest"
        "directus-20250601-192616" = "directus/directus:latest"
        "llm-service-20250601-192616" = "mvs-vr/llm-service:latest"
        "monitoring-service-20250601-192616" = "mvs-vr/monitoring-service:latest"
    }
    
    foreach ($imageFile in $imageTags.Keys) {
        $targetTag = $imageTags[$imageFile]
        Write-ColorOutput "🏷️ Tagging $imageFile as $targetTag..." $Cyan
        
        if ($DryRun) {
            Write-ColorOutput "DRY RUN: Would tag $imageFile as $targetTag" $Yellow
        } else {
            # Get the image ID and tag it properly
            $imageId = ssh -i $SSHKeyPath $Username@$ServerIP "docker images --format '{{.ID}}' | head -1"
            if ($imageId) {
                ssh -i $SSHKeyPath $Username@$ServerIP "docker tag $imageId $targetTag"
                if ($LASTEXITCODE -eq 0) {
                    Write-ColorOutput "✅ Tagged $imageFile as $targetTag" $Green
                } else {
                    Write-ColorOutput "❌ Failed to tag $imageFile" $Red
                }
            }
        }
    }
}

function Restart-StagingServices {
    Write-ColorOutput "🔄 Restarting staging services..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would restart staging services" $Yellow
    } else {
        # Stop services
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose down"
        
        # Start services
        ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose up -d"
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Staging services restarted" $Green
        } else {
            Write-ColorOutput "❌ Failed to restart staging services" $Red
        }
    }
}

function Test-StagingServices {
    Write-ColorOutput "🧪 Testing staging services..." $Cyan
    
    if ($DryRun) {
        Write-ColorOutput "DRY RUN: Would test staging services" $Yellow
        return
    }
    
    Write-ColorOutput "⏳ Waiting 30 seconds for services to start..." $Yellow
    Start-Sleep -Seconds 30
    
    # Check Docker services status
    Write-ColorOutput "🐳 Checking Docker services status..." $Cyan
    ssh -i $SSHKeyPath $Username@$ServerIP "cd /root/stagingmvs && docker-compose ps"
    
    # Test endpoints
    $testUrls = @(
        "http://stagingmvs.kanousai.com/health",
        "http://stagingmvs.kanousai.com/api/health"
    )
    
    foreach ($url in $testUrls) {
        Write-ColorOutput "🔍 Testing $url..." $Cyan
        $response = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' $url"
        if ($response -eq "200") {
            Write-ColorOutput "✅ $url - OK" $Green
        } else {
            Write-ColorOutput "❌ $url - Failed (HTTP $response)" $Red
        }
    }
}

# Main execution
Write-ColorOutput "🔧 MVS-VR Docker Images Fix" $Cyan
Write-ColorOutput "============================" $Cyan

if ($DryRun) {
    Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" $Yellow
}

# Test SSH connection
Write-ColorOutput "🔍 Testing SSH connection..." $Cyan
ssh -i $SSHKeyPath -o ConnectTimeout=10 -o BatchMode=yes $Username@$ServerIP "echo 'SSH connection successful'" | Out-Null

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput "✅ SSH connection successful" $Green
} else {
    Write-ColorOutput "❌ SSH connection failed" $Red
    exit 1
}

# Execute fix steps
Fix-DockerImages
List-DockerImages
Tag-DockerImages
Restart-StagingServices
Test-StagingServices

Write-ColorOutput "✅ Docker images fix completed!" $Green
Write-ColorOutput "📋 Next steps:" $Cyan
Write-ColorOutput "1. Verify all services are running: docker-compose ps" $Yellow
Write-ColorOutput "2. Check service logs if needed: docker-compose logs [service-name]" $Yellow
Write-ColorOutput "3. Test staging endpoints: http://stagingmvs.kanousai.com" $Yellow
